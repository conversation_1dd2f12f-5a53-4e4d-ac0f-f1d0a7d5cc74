import os
import asyncio

from dotenv import load_dotenv

from anotherme.run import create_agent
from anotherme.utils.vis import get_adjusted_window_size
from anotherme.utils.logging_setting import set_logging

import sys

sys.path.append(
    os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "anotherme"))
)


load_dotenv(override=True)


recordings_root = "./recordings"
model_id = "gpt-4o"

recordings_dir = os.path.join(recordings_root, "example")
browser_log_dir = os.path.join(recordings_dir, "browser_logs")
os.makedirs(recordings_dir, exist_ok=True)
log_file = os.path.join(recordings_dir, "output.log")
main_file = os.path.join(recordings_dir, "main.log")
with open(log_file, "w") as f:
    f.write("")
with open(main_file, "w") as f:
    f.write("")
set_logging(log_file)

window_size = get_adjusted_window_size(char_width=7.5)

agent = create_agent(
    model_id=model_id,
    browser_log_dir=browser_log_dir,
    window_size=window_size,
    sub_agents=["browser"],
)
agent.logger.register_log_file(main_file, log_file)


initial_actions = [
    {
        "go_to_url": {
            "url": "https://ladieslane.shop/products/solarcrown?fbclid=IwY2xjawKgzuxleHRuA2FlbQIxMABicmlkETBJbTJSUXN2bFVNaFpOM0o1AR4tJqIwVx4xFx_OwkjcCI0WUfqLIwEW55f43g41gTjmEdg6pLqI8sWIZeCXwg_aem_S-IYjsYsk9mPK2t2cy1YuQ"
        }
    }
]
initial_actions = agent.managed_agents["browseruse_agent"]._convert_initial_actions(
    initial_actions
)


async def main():
    await agent.managed_agents["browseruse_agent"].browser_session.get_current_page()
    result = await agent.managed_agents["browseruse_agent"].multi_act(
        initial_actions, check_for_new_elements=False
    )
    agent.managed_agents["browseruse_agent"].state.last_result = result

    # import pdb
    # pdb.set_trace()
    # await agent.managed_agents['browseruse_agent'].excute_task(task="根据网页中的图片，确定当前网站是卖什么的页面")
    # await agent.managed_agents['browseruse_agent'].excute_task(task="确定当前网站是卖什么的页面")
    # await multimodal_webpage_extraction(agent, "确定当前网站是卖什么的页面", web_video=True)
    # await agent.managed_agents['browseruse_agent'].excute_task(task="确定当前网站的图片显示了什么")
    # await agent.managed_agents['browseruse_agent'].excute_task(task="视频介绍了什么")
    await agent.managed_agents["browseruse_agent"].excute_task(
        task="网页中的文字，图片，视频分别介绍了什么"
    )

    await asyncio.sleep(3)
    await agent.managed_agents["browseruse_agent"].browser_context.__aexit__(
        None, None, None
    )
    # await agent.managed_agents['browseruse_agent'].browser.close()


asyncio.run(main())
