{"id": "opendining-6", "goal": "Select any restaurant and try finishing the booking with the phone number \"*******\". Report whether the booking was successful or if there was an error.", "website": {"id": "opendining", "name": "OpenDining", "similarTo": "OpenTable", "previewImage": "/websitePreviews/opendining_preview.jpg", "url": "https://evals-opendining.vercel.app/"}, "difficulty": "easy", "challengeType": "retrieval-action", "possible": true, "evals": [{"type": "llm_boolean", "expected_value": true, "rubric": "Does the response indicate that the booking failed or there was a validation error due to the invalid phone number format '*******'?"}], "points": 1, "config": {}}