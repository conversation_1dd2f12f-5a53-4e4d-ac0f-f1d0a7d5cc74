{"id": "fly-unified-10", "goal": "Try to book a one-way flight for a minor under 18 years old from San Francisco to Chicago in Basic Economy on December 18th at 10:00. Ensure no seat selection is made. Tell me if the booking process allows this.\nPassenger: <PERSON>\nDate of Birth: 01/15/2010\nSex: Male\nSeat Selection: No\nPayment: Credit Card (9999 8888 7777), Exp: 03/30, Address: 987 Cedar St, Chicago, IL, 60601, USA, Phone: ************, Email: <EMAIL>.", "website": {"id": "fly-unified", "name": "Fly Unified", "similarTo": "United Airlines", "previewImage": "/websitePreviews/fly-unified_preview.jpg", "url": "https://evals-fly-unified.vercel.app/"}, "difficulty": "medium", "challengeType": "action", "possible": true, "evals": [{"type": "llm_boolean", "expected_value": true, "rubric": "Does the response indicate that the booking system does not allow booking a flight for an unaccompanied minor (under 18 years old)?"}], "points": 1, "config": {}}