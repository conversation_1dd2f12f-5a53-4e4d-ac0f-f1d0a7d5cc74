{"id": "zilloft-6", "goal": "Select a property listed in San Francisco as \"Condos\" within a price range under $300,000 and request a tour for tomorrow at 4:00 PM. Use these contact details: Name: <PERSON>, Email: <EMAIL>, Phone: ************.", "website": {"id": "zilloft", "name": "Zilloft", "similarTo": "Zillow", "previewImage": "/websitePreviews/zilloft_preview.jpg", "url": "https://evals-zilloft.vercel.app/"}, "difficulty": "medium", "challengeType": "action", "possible": true, "evals": [{"description": "tour request submitted", "type": "jmespath", "expected_value": true, "query": "differences.requestTours.added != null && length(differences.requestTours.added) > `0`"}, {"description": "correct name provided", "type": "jmespath", "expected_value": "<PERSON>", "query": "differences.requestTours.added.\"0\".requestTourData.formValues.name"}, {"description": "correct email provided", "type": "jmespath", "expected_value": "<EMAIL>", "query": "differences.requestTours.added.\"0\".requestTourData.formValues.email"}, {"description": "correct phone provided", "type": "jmespath", "expected_value": "************", "query": "differences.requestTours.added.\"0\".requestTourData.formValues.phone"}, {"description": "correct time selected", "type": "jmespath", "expected_value": "4:00 PM", "query": "differences.requestTours.added.\"0\".requestTourData.options[0].time"}], "points": 1, "config": {}}