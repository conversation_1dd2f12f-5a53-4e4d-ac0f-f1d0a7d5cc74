{"id": "zilloft-9", "goal": "Find the most expensive home listed in San Francisco with 4+ bedrooms and request a tour for 6:00 PM on the earliest possible date. Use these contact details: Name: <PERSON>, Email: da<PERSON><PERSON>@example.com, Phone: ************. What is the price of this home?", "website": {"id": "zilloft", "name": "Zilloft", "similarTo": "Zillow", "previewImage": "/websitePreviews/zilloft_preview.jpg", "url": "https://evals-zilloft.vercel.app/"}, "difficulty": "hard", "challengeType": "retrieval-action", "possible": true, "description": "eval not implemented yet", "evals": [{"description": "tour request submitted", "type": "jmespath", "expected_value": true, "query": "differences.requestTours.added != null && length(differences.requestTours.added) > `0`"}, {"description": "correct name provided", "type": "jmespath", "expected_value": "<PERSON>", "query": "differences.requestTours.added.\"0\".requestTourData.formValues.name"}, {"description": "correct email provided", "type": "jmespath", "expected_value": "david<PERSON>@example.com", "query": "differences.requestTours.added.\"0\".requestTourData.formValues.email"}, {"description": "correct phone provided", "type": "jmespath", "expected_value": "************", "query": "differences.requestTours.added.\"0\".requestTourData.formValues.phone"}, {"description": "correct time selected", "type": "jmespath", "expected_value": "6:00 PM", "query": "differences.requestTours.added.\"0\".requestTourData.options[0].time"}, {"description": "found correct price", "type": "llm_boolean", "expected_value": true, "rubric": "Does the answer correctly state that the price of the most expensive home in San Francisco with 4+ bedrooms is $4,985,000?"}], "points": 1, "config": {}}