{"id": "fly-unified-4", "goal": "Book me a round-trip flight from Providence (Rhode Island) to Indianapolis, departing on December 5th, 2024 at 08:00 and returning on December 9th at 14:00.\nPassenger: <PERSON>\nDate of Birth: 02/14/1995\nSex: Female\nSeat Selection: Yes (Window seat)\nPayment: Credit Card (***************), Exp: 06/26, security code: 345 Address: 456 Elm St, Miami, FL, 33101, USA, Phone: ************, Email: <EMAIL>.", "website": {"id": "fly-unified", "name": "Fly Unified", "similarTo": "United Airlines", "previewImage": "/websitePreviews/fly-unified_preview.jpg", "url": "https://evals-fly-unified.vercel.app/"}, "difficulty": "medium", "challengeType": "action", "possible": true, "evals": [{"description": "correct departure city", "type": "jmespath", "expected_value": "Providence", "query": "differences.bookedFlights.added.\"0\".flight.outboundFlight.info.from.destination.city"}, {"description": "correct destination city", "type": "jmespath", "expected_value": "Indianapolis", "query": "differences.bookedFlights.added.\"0\".flight.outboundFlight.info.to.destination.city"}, {"description": "correct return departure city", "type": "jmespath", "expected_value": "Indianapolis", "query": "differences.bookedFlights.added.\"0\".flight.returnFlight.info.from.destination.city"}, {"description": "correct return destination city", "type": "jmespath", "expected_value": "Providence", "query": "differences.bookedFlights.added.\"0\".flight.returnFlight.info.to.destination.city"}, {"description": "correct passenger first name", "type": "jmespath", "expected_value": "<PERSON>", "query": "differences.bookedFlights.added.\"0\".passengersInfo[0].firstName"}, {"description": "correct passenger last name", "type": "jmespath", "expected_value": "<PERSON>", "query": "differences.bookedFlights.added.\"0\".passengersInfo[0].lastName"}, {"description": "correct departure time", "type": "jmespath", "expected_value": "2024-12-05T16:00:00.000Z", "query": "differences.bookedFlights.added.\"0\".flight.outboundFlight.info.segments[0].departureTime"}, {"description": "correct return time", "type": "jmespath", "expected_value": "2024-12-09T22:00:00.000Z", "query": "differences.bookedFlights.added.\"0\".flight.returnFlight.info.segments[0].departureTime"}], "points": 1, "config": {}}