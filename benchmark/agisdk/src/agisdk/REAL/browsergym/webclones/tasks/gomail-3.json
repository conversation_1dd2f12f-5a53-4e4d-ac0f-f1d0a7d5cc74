{"id": "gomail-3", "goal": "Compose a new <NAME_EMAIL> with the subject \"Meeting Notes\" and body \"Please find the meeting notes attached.\"", "website": {"id": "gomail", "name": "GoMail", "similarTo": "Gmail", "previewImage": "/websitePreviews/gomail_preview.jpg", "url": "https://evals-gomail.vercel.app/"}, "difficulty": "easy", "challengeType": "action", "possible": true, "evals": [{"possible": true, "type": "jmespath", "expected_value": "<EMAIL>", "query": "differences.emails.added[0].to[0]"}, {"possible": true, "description": "correct subject", "type": "jmespath", "expected_value": "Meeting Notes", "query": "differences.emails.added[0].subject"}, {"possible": true, "description": "correct email content", "type": "jmespath", "expected_value": "<p>Please find the meeting notes attached.</p>", "query": "differences.emails.added[0].content"}], "points": 1, "config": {}}