{"id": "zilloft-3", "goal": "Find a home in San Diego priced under $150,000 with at least 2 bedrooms and request a tour. Use these details: Contact Name: <PERSON>, Email: joh<PERSON><PERSON>@example.com, Phone: ************, Tour Time: 2:00 PM, Tour Date: First available.", "website": {"id": "zilloft", "name": "Zilloft", "similarTo": "Zillow", "previewImage": "/websitePreviews/zilloft_preview.jpg", "url": "https://evals-zilloft.vercel.app/"}, "difficulty": "easy", "challengeType": "retrieval-action", "possible": true, "evals": [{"description": "correct contact name", "type": "jmespath", "expected_value": "<PERSON>", "query": "differences.requestTours.added.\"0\".requestTourData.formValues.name"}, {"description": "correct email address", "type": "jmespath", "expected_value": "<EMAIL>", "query": "differences.requestTours.added.\"0\".requestTourData.formValues.email"}, {"description": "correct phone number", "type": "jmespath", "expected_value": "************", "query": "differences.requestTours.added.\"0\".requestTourData.formValues.phone"}, {"description": "correct tour time", "type": "jmespath", "expected_value": "2:00 PM", "query": "differences.requestTours.added.\"0\".requestTourData.options[0].time"}], "points": 1, "config": {}}