import os
import asyncio

from dotenv import load_dotenv

from anotherme.run import create_agent
from anotherme.utils.vis import get_adjusted_window_size
from anotherme.utils.logging_setting import set_logging
from anotherme.browser_use.dom.service import DomService

import sys

sys.path.append(
    os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "anotherme"))
)


load_dotenv(override=True)


recordings_root = "./recordings"
model_id = "gpt-4o"
storage_state_path = "./dev/pgy.json"
recordings_dir = os.path.join(recordings_root, "example")
browser_log_dir = os.path.join(recordings_dir, "browser_logs")
os.makedirs(recordings_dir, exist_ok=True)
log_file = os.path.join(recordings_dir, "output.log")
main_file = os.path.join(recordings_dir, "main.log")
with open(log_file, "w") as f:
    f.write("")
with open(main_file, "w") as f:
    f.write("")
set_logging(log_file)

window_size = get_adjusted_window_size(char_width=7.5)

agent = create_agent(
    model_id=model_id,
    browser_log_dir=browser_log_dir,
    window_size=window_size,
    storage_state=storage_state_path,
    sub_agents=["browser"],
)
agent.logger.register_log_file(main_file, log_file)


initial_actions = [
    {"go_to_url": {"url": "https://pgy.xiaohongshu.com/solar/pre-trade/note/kol"}}
]
initial_actions = agent.managed_agents["browseruse_agent"]._convert_initial_actions(
    initial_actions
)


async def main():
    browseruse_agent = agent.managed_agents["browseruse_agent"]
    await browseruse_agent.browser_session.get_current_page()
    await browseruse_agent.multi_act(initial_actions)
    await browseruse_agent.browser_session._wait_for_page_and_frames_load()
    page = await browseruse_agent.browser_session.get_current_page()

    # html: html source of the page
    # content: clickable elements of the page
    html = await page.content()
    dom_service = DomService(page)
    content = await dom_service.get_clickable_elements(
        focus_element=-1,
        viewport_expansion=browseruse_agent.browser_session.browser_profile.viewport_expansion,
        highlight_elements=browseruse_agent.browser_session.browser_profile.highlight_elements,
    )
    await browseruse_agent.browser_session.get_state_summary(
        cache_clickable_elements_hashes=True
    )
    html = html
    content = content
    ### TODO: find the index of the element that can redirect to the required blogger
    """
        Your code here.
    """
    ### replace the value of index for val
    index = 20
    click_actions = [{"click_element_by_index": {"index": index, "xpath": None}}]
    click_actions = browseruse_agent._convert_initial_actions(click_actions)
    await browseruse_agent.multi_act(click_actions)
    while True:
        await asyncio.sleep(3)


asyncio.run(main())
