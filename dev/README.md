# DOM Parser

A simple DOM parser that loads local storage state and navigates to Xiaohongshu pages for parsing.

## Features

- ✅ **Storage State Support** - Automatically loads login state from local files
- ✅ **DOM Parsing** - Uses buildDomTree.js for structured DOM extraction
- ✅ **Auto Navigation** - Direct navigation to target pages
- ✅ **Structured Output** - HTML + DOM tree structure

## Quick Start

```bash
# Run with default storage state (./dev/pgy.json)
python dev/xiaohongshu_simple_parser.py

# Run with custom storage state file
python dev/xiaohongshu_simple_parser.py --storage-state /path/to/your/storage_state.json

# Run in headless mode
python dev/xiaohongshu_simple_parser.py --headless
```
