import pandas as pd
import re
import os
import ast
import urllib.parse


def is_valid_url(url):
    """检查字符串是否是有效的URL"""
    if not isinstance(url, str):
        return False

    # 简单的URL验证正则表达式，适应Facebook重定向链接
    url_pattern = re.compile(
        r"^(https?://)?(www\.|l\.)?"  # http:// 或 https:// 或 www. 或 l.
        r"[a-zA-Z0-9][-a-zA-Z0-9]*(\.[a-zA-Z0-9][-a-zA-Z0-9]*)+"  # 域名
        r"(/[-a-zA-Z0-9%_.~#+&?=]*)*"  # 路径和查询参数，更宽松的匹配
    )
    return bool(url_pattern.match(url))


def extract_urls_from_text(text):
    """从文本中提取URL列表，处理可能的列表格式"""
    if not isinstance(text, str):
        return []

    # 尝试解析为Python列表
    text = text.strip()
    if text.startswith("[") and text.endswith("]"):
        try:
            # 尝试使用ast.literal_eval安全地解析列表
            urls = ast.literal_eval(text)
            if isinstance(urls, list):
                return urls
        except (SyntaxError, ValueError):
            # 如果解析失败，尝试手动解析
            pass

    # 手动解析列表格式
    if text.startswith("['") and text.endswith("']"):
        # 移除首尾的 [' 和 ']
        text = text[2:-2]
        # 按 ', ' 分割
        return [url.strip() for url in text.split("', '")]

    # 如果不是列表格式，则将整个文本作为单个URL返回
    return [text]


def process_excel_file(file_path, output_path=None):
    """
    处理Excel文件:
    1. 读取文件
    2. 剔除blogger_links为空列表[]的行
    3. 剔除product_links中不是链接的行
    4. 剔除每一行的blogger_links和product_links重复的选项
    """
    print(f"正在处理文件: {file_path}")

    # 确定输出路径
    if output_path is None:
        base_name = os.path.basename(file_path)
        name, ext = os.path.splitext(base_name)
        output_path = os.path.join(os.path.dirname(file_path), f"{name}_processed{ext}")

    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 检查必要的列是否存在
        required_columns = ["blogger_links", "product_links"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"错误: 文件缺少必要的列: {', '.join(missing_columns)}")
            return

        # 原始行数
        original_row_count = len(df)
        print(f"原始数据行数: {original_row_count}")

        # 处理前先填充NaN值为空字符串
        df["blogger_links"] = df["blogger_links"].fillna("")
        df["product_links"] = df["product_links"].fillna("")

        # 1. 剔除blogger_links为空列表[]的行
        valid_blogger_links = []
        for i, row in df.iterrows():
            blogger_links_text = str(row["blogger_links"]).strip()

            # 检查是否为空列表
            is_empty_list = blogger_links_text == "[]" or blogger_links_text == ""

            # 如果不是空列表，则保留
            valid_blogger_links.append(not is_empty_list)

        # 应用过滤
        df = df[valid_blogger_links]
        filtered_row_count_1 = len(df)
        print(f"剔除空blogger_links后的行数: {filtered_row_count_1}")
        print(f"剔除的行数: {original_row_count - filtered_row_count_1}")

        # 2. 剔除product_links中不是链接的行
        valid_product_links = []
        for i, row in df.iterrows():
            product_links_text = str(row["product_links"]).strip()

            # 提取产品链接列表
            product_urls = extract_urls_from_text(product_links_text)

            # 检查是否至少有一个有效链接
            has_valid_url = False
            for url in product_urls:
                if is_valid_url(url):
                    has_valid_url = True
                    break

            valid_product_links.append(has_valid_url)

        # 应用过滤
        df = df[valid_product_links]
        filtered_row_count_2 = len(df)
        print(f"剔除无效product_links后的行数: {filtered_row_count_2}")
        print(f"剔除的行数: {filtered_row_count_1 - filtered_row_count_2}")

        # 3. 剔除每一行的blogger_links和product_links重复的选项
        for i, row in df.iterrows():
            blogger_links_text = str(row["blogger_links"]).strip()
            product_links_text = str(row["product_links"]).strip()

            # 提取链接列表
            blogger_urls = extract_urls_from_text(blogger_links_text)
            product_urls = extract_urls_from_text(product_links_text)

            # 提取基本URL部分进行比较（忽略查询参数）
            def extract_base_url(url):
                # 对于Facebook重定向链接，提取目标URL
                if "l.facebook.com/l.php?u=" in url:
                    # 提取u参数的值
                    match = re.search(r"u=(.*?)(?:&|$)", url)
                    if match:
                        target_url = match.group(1)
                        # URL解码
                        target_url = urllib.parse.unquote(target_url)
                        # 移除fbclid参数
                        target_url = re.sub(r"\?fbclid=.*", "", target_url)
                        target_url = re.sub(r"&fbclid=.*", "", target_url)
                        return target_url
                # 移除查询参数
                return re.sub(r"\?.*", "", url)

            # 提取基本URL进行比较
            blogger_base_urls = [extract_base_url(url) for url in blogger_urls]
            product_base_urls = [extract_base_url(url) for url in product_urls]

            # 移除重复链接（基于基本URL比较）
            unique_product_urls = []
            for j, url in enumerate(product_urls):
                if product_base_urls[j] not in blogger_base_urls:
                    unique_product_urls.append(url)

            # 如果移除重复后产品链接为空，则设置为空字符串
            # 否则，保持原来的格式（列表或单个链接）
            if not unique_product_urls:
                df.at[i, "product_links"] = ""
            elif len(product_urls) != len(unique_product_urls):
                # 如果有链接被移除，更新产品链接
                if len(product_urls) > 1 or product_links_text.startswith("["):
                    # 保持列表格式
                    df.at[i, "product_links"] = str(unique_product_urls)
                else:
                    # 单个链接
                    df.at[i, "product_links"] = (
                        unique_product_urls[0] if unique_product_urls else ""
                    )

        # 保存处理后的文件
        df.to_excel(output_path, index=False)
        print(f"处理完成，结果已保存至: {output_path}")

        return df

    except Exception as e:
        print(f"处理文件时出错: {str(e)}")
        return None


if __name__ == "__main__":
    # 替换为你的文件路径
    file_path = "/Users/<USER>/Desktop/code/anotherme/ListAdContentSchema_4.1.xlsx"
    process_excel_file(file_path)
