import os
import sys
import time
import pandas as pd
import argparse
import multiprocessing
from multiprocessing import Process, Manager
import subprocess
import logging
import json
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("parallel_excel_filler.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser(description="并行执行Excel填充任务")
    parser.add_argument(
        "--excel_file",
        type=str,
        default="/Users/<USER>/Desktop/code/anotherme/ListAdContentSchema_4.1_processed.xlsx",
        help="要处理的Excel文件路径",
    )
    parser.add_argument(
        "--task_file",
        type=str,
        default="./helpers/tasks/rpa/tested/fillexcel.md",
        help="任务描述文件路径",
    )
    parser.add_argument(
        "--model_id",
        type=str,
        default="openrouter/anthropic/claude-3.7-sonnet",
        help="使用的模型ID",
    )
    parser.add_argument(
        "--concurrent_limit", type=int, default=3, help="并行执行的agent数量"
    )
    parser.add_argument(
        "--start_row",
        type=int,
        default=0,
        help="从Excel的哪一行开始处理（0表示第一行数据）",
    )
    parser.add_argument(
        "--end_row",
        type=int,
        default=None,
        help="处理到Excel的哪一行结束（None表示处理到最后）",
    )
    parser.add_argument(
        "--timeout", type=int, default=1800, help="每个任务的超时时间（秒）"
    )
    parser.add_argument(
        "--headless", action="store_true", help="是否使用无头模式运行浏览器"
    )
    parser.add_argument(
        "--use_terminal_aware", action="store_true", help="是否使用终端感知模式"
    )
    return parser.parse_args()


def read_excel_file(file_path, start_row=0, end_row=None):
    """读取Excel文件并返回需要处理的行"""
    logger.info(f"读取Excel文件: {file_path}")
    try:
        df = pd.read_excel(file_path)
        # 检查必要的列是否存在
        required_columns = ["blogger_links", "product_links"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Excel文件缺少必要的列: {', '.join(missing_columns)}")
            return None

        # 截取指定范围的行
        if end_row is None:
            end_row = len(df)
        df_subset = df.iloc[start_row:end_row].copy()

        logger.info(f"成功读取Excel文件，处理行数: {len(df_subset)}")
        return df_subset
    except Exception as e:
        logger.error(f"读取Excel文件时出错: {e}")
        return None


def create_task_for_row(row_data, row_index, task_template):
    row_json = row_data.to_json()

    task_description = {
        "task_type": "excel_fill",
        "row_index": row_index,
        "row_data": json.loads(row_json),
        "task_template": task_template,
    }

    return task_description


def run_agent_process(task_queue, result_queue, args, process_id):
    """在单独的进程中运行agent"""
    logger.info(f"进程 {process_id} 启动")

    while not task_queue.empty():
        try:
            # 尝试从队列获取任务，如果队列为空则退出
            try:
                task = task_queue.get(block=False)
            except Exception:
                break

            row_index = task["row_index"]

            # 获取行数据
            row_data = task["row_data"]
            blogger_links = row_data.get("blogger_links", "")
            product_links = row_data.get("product_links", "")
            product_info = row_data.get("advertising_content", "")

            # 创建包含列索引的文件夹
            column_index = row_index + args.start_row
            output_dir = os.path.join("./recordings/parallel", f"column_{column_index}")
            os.makedirs(output_dir, exist_ok=True)

            current_time = datetime.now().strftime("%m-%d-%H-%M")
            temp_task_file = os.path.join(output_dir, f"task_{current_time}.md")

            logger.info(
                f"进程 {process_id} 开始处理行 {row_index}，任务文档为: {temp_task_file}"
            )
            # 读取原始任务模板
            task_template = task["task_template"]

            # 替换任务模板中的占位符
            modified_template = task_template.replace(
                "填充行索引：<行索引>, 博主链接：<博主链接>, 产品链接：<产品链接>, 广告信息：<广告信息>",
                f"填充行索引：{column_index}, 博主链接：{blogger_links}, 产品链接：{product_links}, 广告信息：{product_info}",
            )

            # 写入修改后的任务文件
            with open(temp_task_file, "w", encoding="utf-8") as f:
                f.write(modified_template)

            # 构建命令
            cmd = [
                sys.executable,  # 当前Python解释器路径
                "anotherme/demo.py",
                "--task_file",
                temp_task_file,
                "--model_id",
                args.model_id,
                "--recordings_dir",
                output_dir,
                *(["--headless"] if args.headless else []),
                *(["--use_terminal_aware"] if args.use_terminal_aware else []),
                "--storage_state",
                "./helpers/.auth/fb1688.json",
            ]

            try:
                start_time = time.time()
                process = subprocess.Popen(
                    cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
                )

                # 设置超时
                try:
                    stdout, stderr = process.communicate(timeout=args.timeout)
                    exit_code = process.returncode

                    # 记录结果
                    result = {
                        "row_index": row_index,
                        "column_index": column_index,
                        "success": exit_code == 0,
                        "exit_code": exit_code,
                        "execution_time": time.time() - start_time,
                        "task_file": temp_task_file,
                    }

                    if exit_code != 0:
                        logger.error(
                            f"进程 {process_id} 处理行 {row_index} (列索引 {column_index}) 失败: {stderr}"
                        )
                        result["error"] = stderr
                    else:
                        logger.info(
                            f"进程 {process_id} 处理行 {row_index} (列索引 {column_index}) 成功，耗时: {result['execution_time']:.2f}秒"
                        )

                    result_queue.put(result)

                except subprocess.TimeoutExpired:
                    process.kill()
                    logger.error(
                        f"进程 {process_id} 处理行 {row_index} (列索引 {column_index}) 超时"
                    )
                    result = {
                        "row_index": row_index,
                        "column_index": column_index,
                        "success": False,
                        "error": "任务执行超时",
                        "execution_time": args.timeout,
                        "task_file": temp_task_file,
                    }
                    result_queue.put(result)

            except Exception as e:
                logger.error(f"进程 {process_id} 执行命令时出错: {e}")
                result = {
                    "row_index": row_index,
                    "column_index": column_index,
                    "success": False,
                    "error": str(e),
                    "execution_time": time.time() - start_time,
                    "task_file": temp_task_file,
                }
                result_queue.put(result)

        except Exception as e:
            logger.error(f"进程 {process_id} 处理任务时出错: {e}")

    logger.info(f"进程 {process_id} 完成所有任务")


def main():
    args = parse_args()

    df = read_excel_file(args.excel_file, args.start_row, args.end_row)
    if df is None:
        return

    try:
        with open(args.task_file, "r", encoding="utf-8") as f:
            task_template = f.read()
    except Exception as e:
        logger.error(f"读取任务模板文件时出错: {e}")
        return

    manager = Manager()
    task_queue = manager.Queue()
    result_queue = manager.Queue()

    for i, (_, row) in enumerate(df.iterrows()):
        task = create_task_for_row(row, i + args.start_row, task_template)
        task_queue.put(task)

    logger.info(f"创建了 {task_queue.qsize()} 个任务")

    # 创建并启动进程
    processes = []
    for i in range(min(args.concurrent_limit, task_queue.qsize())):
        p = Process(target=run_agent_process, args=(task_queue, result_queue, args, i))
        processes.append(p)
        p.start()

    # 等待所有进程完成
    for p in processes:
        p.join()

    # 收集结果
    results = []
    while not result_queue.empty():
        results.append(result_queue.get())

    # 按行索引排序结果
    results.sort(key=lambda x: x["row_index"])

    summary = {
        "total_rows": len(results),
        "successful_rows": sum(1 for r in results if r["success"]),
        "total_time": sum(r["execution_time"] for r in results),
    }

    logger.info(
        f"所有任务完成。成功: {summary['successful_rows']}/{summary['total_rows']}"
    )
    logger.info(f"总耗时: {summary['total_time']:.2f}秒")


if __name__ == "__main__":
    multiprocessing.freeze_support()  # Windows支持
    main()
