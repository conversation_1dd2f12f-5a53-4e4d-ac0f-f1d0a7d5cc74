import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional


from anotherme.browser_use.agent.service import Agent
from anotherme.browser_use.agent.views import AgentOutput
from anotherme.browser_use.browser.profile import (
    BrowserProfile,
    BROWSERUSE_PROFILES_DIR,
)

logger = logging.getLogger("anotherme.browser_use.agent.service")


class BrowserUseAgent(Agent):
    def __init__(
        self,
        llm,
        task: str = "",
        name: Optional[str] = None,
        inputs: Optional[Dict[str, Any]] = None,
        description: Optional[str] = None,
        log_dir: Optional[str] = "./recordings",
        window_size: Optional[Dict[str, int]] = None,
        headless: bool = False,
        storage_state: Optional[str] = None,
        window_position: Optional[Dict[str, int]] = None,
        **kwargs,
    ):
        browser_profile = BrowserProfile(
            headless=headless,
            window_size=window_size,
            user_data_dir=None
            if storage_state
            else BROWSERUSE_PROFILES_DIR / "default",
            storage_state=storage_state,
        )
        super().__init__(task=task, llm=llm, browser_profile=browser_profile, **kwargs)

        self.name = name or "Browser Use Agent"
        self.inputs = inputs
        self.description = description
        self.log_dir = log_dir
        os.makedirs(self.log_dir, exist_ok=True)

        self.total_input_token_count = 0
        self.total_output_token_count = 0
        self.total_llm_duration = 0

        self.current_message = ""
        self.current_log_idx = 0
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def _format_tool_calls(self, tool_calls: List[Dict[str, Any]]) -> str:
        """
        Formats the 'args' part of a list of tool calls.
        This function only outputs the beautified JSON content of 'args' from each tool call.
        Each output line (including JSON lines or messages) will have a base indent.
        """
        base_indent = "  "

        if not tool_calls:
            return f"{base_indent}(No tool call data to display)"

        all_formatted_args_blocks = []

        for i, call in enumerate(tool_calls):
            args = call.get("args")
            current_call_block_lines = []

            if args is not None:  # If args exist (can be an empty dict/list)
                try:
                    # Use indent=2 to beautify JSON output.
                    # ensure_ascii=False ensures that non-ASCII characters (e.g., Chinese) are displayed correctly.
                    args_json_str = json.dumps(args, indent=2, ensure_ascii=False)
                    # Add base indent to each line of the formatted JSON string.
                    for line in args_json_str.splitlines():
                        current_call_block_lines.append(f"{base_indent}{line}")
                except TypeError:
                    current_call_block_lines.append(
                        f"{base_indent}(Tool call {i + 1} args cannot be serialized to JSON: Type {type(args)})"
                    )
            else:  # args is None
                current_call_block_lines.append(
                    f"{base_indent}(Tool call {i + 1} did not provide args)"
                )

            all_formatted_args_blocks.append("\n".join(current_call_block_lines))

        if not all_formatted_args_blocks:
            return f"{base_indent}(No args found in any tool calls)"

        return "\n\n".join(all_formatted_args_blocks)

    def _log_llm_input(self, input_messages: List[Any]) -> None:
        """
        Enhanced LLM input logging with browser_use style formatting.
        Logs detailed message content with emojis and consistent formatting.
        """
        if not input_messages:
            return

        # Add terminal width-based separator (similar to service.py style)
        import shutil

        term_width = shutil.get_terminal_size((80, 20)).columns
        separator = "─" * term_width

        # Log opening separator and header (use print for clean separator)
        print(separator)

        # Get message type emoji mapping
        def get_message_emoji(message_type: str) -> str:
            emoji_map = {
                "systemmessage": "⚙️",
                "humanmessage": "💬",
                "aimessage": "🧠",
                "toolmessage": "🔨",
                "system": "⚙️",
                "human": "💬",
                "ai": "🧠",
                "tool": "🔨",
            }
            return emoji_map.get(message_type.lower(), "❓")

        # Log header with message count
        message_count = len(input_messages)
        logger.info(f"📜 LLM Input Messages ({message_count} messages):")

        for idx, message_obj in enumerate(input_messages):
            try:
                # Determine message type and role
                if hasattr(message_obj, "__class__"):
                    message_type = message_obj.__class__.__name__
                    role = message_type.replace("Message", "").lower()
                elif hasattr(message_obj, "type"):
                    role = str(getattr(message_obj, "type", "unknown")).lower()
                    message_type = f"{role}message"
                else:
                    role = "unknown"
                    message_type = "unknown"

                emoji = get_message_emoji(message_type)

                # Format message header
                msg_header = f"{emoji} [{idx + 1:2d}] {role.upper()}"

                # Extract and format content
                content_parts = []

                if role == "system":
                    content = getattr(message_obj, "content", "")
                    if isinstance(content, str) and content.strip():
                        content_parts.append(f"System prompt: {content}")
                    else:
                        content_parts.append(
                            "System prompt: [Empty or invalid content]"
                        )

                elif role == "human":
                    content = getattr(message_obj, "content", "")
                    if isinstance(content, str):
                        # Look for current state section for better formatting
                        if "[Current state starts here]" in content:
                            state_start = content.find("[Current state starts here]")
                            before_state = content[:state_start].strip()
                            # state_content = content[state_start:]
                            if before_state:
                                content_parts.append(f"Task: {before_state}")
                            content_parts.append(
                                "Browser state: [Current state provided]"
                            )
                        else:
                            content_parts.append(f"User input: {content}")

                    elif isinstance(content, list):
                        text_parts = []
                        has_images = False
                        for part in content:
                            if isinstance(part, dict):
                                if part.get("type") == "text":
                                    text_parts.append(part.get("text", ""))
                                elif part.get("type") == "image_url":
                                    has_images = True

                        if text_parts:
                            combined_text = " ".join(text_parts)
                            content_parts.append(f"User input: {combined_text}")

                        if has_images:
                            content_parts.append("📷 Contains images")

                elif role == "ai":
                    # AI message content
                    ai_content = getattr(message_obj, "content", "")
                    if isinstance(ai_content, str) and ai_content.strip():
                        content_parts.append(f"Response: {ai_content}")

                    # Tool calls
                    tool_calls = getattr(message_obj, "tool_calls", [])
                    if not tool_calls and hasattr(message_obj, "additional_kwargs"):
                        tool_calls = message_obj.additional_kwargs.get("tool_calls", [])

                    if tool_calls:
                        tool_count = len(tool_calls)
                        content_parts.append(
                            f"🔨 {tool_count} tool call{'s' if tool_count > 1 else ''}"
                        )

                        if tool_calls:
                            first_call = tool_calls[0]
                            tool_name = first_call.get("name", "unknown")
                            args = first_call.get("args", {})

                            content_parts.append(f"  {tool_name}():\n")

                            # Add current state details if available
                            if "current_state" in args:
                                state = args["current_state"]
                                if "evaluation_previous_goal" in state:
                                    content_parts.append(
                                        f"    ├─ Eval: {state['evaluation_previous_goal']}\n"
                                    )
                                if "memory" in state:
                                    content_parts.append(
                                        f"    ├─ Memory: {state['memory']}\n"
                                    )
                                if "next_goal" in state:
                                    content_parts.append(
                                        f"    └─ Next: {state['next_goal']}\n"
                                    )

                            # Add action details if available
                            if "action" in args and args["action"]:
                                action = args["action"][0]
                                for action_name, action_args in action.items():
                                    content_parts.append(
                                        f"    └─ Action: {action_name}({action_args})\n"
                                    )

                elif role == "tool":
                    content = getattr(message_obj, "content", "")

                    if isinstance(content, str):
                        content_parts.append(f"Tool result: {content}")
                    else:
                        content_parts.append(
                            f"Tool result: [Non-string content: {type(content)}]"
                        )

                else:
                    # Unknown message type
                    content_parts.append(f"[Unknown message type: {message_type}]")

                # Log the formatted message
                if content_parts:
                    content_str = " | ".join(content_parts)
                    logger.info(f"{msg_header}: {content_str}")
                else:
                    logger.info(f"{msg_header}: [No content]")

            except Exception as e:
                logger.error(f"❌ Error processing message {idx}: {e}", exc_info=True)

        # Log closing separator (use print for clean separator)
        print(separator)

    def _log_response(
        self, response: AgentOutput, invoke_time: float, token_cost: dict
    ) -> None:
        """Log the model's response and cost"""
        if "Success" in response.current_state.evaluation_previous_goal:
            emoji = "👍"
        elif "Failed" in response.current_state.evaluation_previous_goal:
            emoji = "⚠"
        else:
            emoji = "🤷"
        # logger.debug(f"🤖 {emoji} Page summary: {response.current_state.page_summary}")
        logger.info(f"{emoji} Eval: {response.current_state.evaluation_previous_goal}")
        logger.info(f"🧠 Memory: {response.current_state.memory}")
        logger.info(f"🎯 Next goal: {response.current_state.next_goal}")
        for i, action in enumerate(response.action):
            logger.info(
                f"🛠️  Action {i + 1}/{len(response.action)}: {action.model_dump_json(exclude_unset=True)}"
            )
        last_input_token_count = token_cost["input_tokens"]
        last_output_token_count = token_cost["output_tokens"]
        self.total_input_token_count += last_input_token_count
        self.total_output_token_count += last_output_token_count
        self.total_llm_duration += invoke_time
        logger.info(
            f"[Total Duration {self.total_llm_duration:.2f} seconds | Total Input tokens: {self.total_input_token_count:,} | Total Output tokens: {self.total_output_token_count:,} ]"
        )
        logger.info(
            f"[LLM Duration {invoke_time:.2f} seconds | Current Input tokens: {last_input_token_count:,} | Current Output tokens: {last_output_token_count:,} ]"
        )

    def _log_llm_call_info(self, input_messages, method: str) -> None:
        """Override parent method to add custom LLM input logging before the standard logging"""
        # self._log_llm_input(input_messages)

        super()._log_llm_call_info(input_messages, method)

    def update_browser_log_path(self):
        """
        update the log file path for the browser use agent
        """
        self.current_log_idx = getattr(self, "current_log_idx", 0)
        new_path = f"{self.log_dir}/log_browser_use_{self.current_log_idx}.log"
        # logger = logging.getLogger(__name__)

        os.makedirs(os.path.dirname(new_path), exist_ok=True)
        with open(new_path, "w") as f:
            f.write("")
        logger_handlers = logger.handlers[:1]
        logger_ref = logger_handlers[0]
        log_level_ref = logger_ref.level
        formatter_ref = logger_ref.formatter
        file_handler = logging.FileHandler(new_path, mode="a", encoding="utf-8")
        file_handler.setLevel(log_level_ref)
        file_handler.setFormatter(formatter_ref)

        logger.addHandler(file_handler)

        self.current_log_idx += 1

    async def _update_message(self) -> str:
        """Get the message from the agent including all steps since task_start_step"""
        try:
            # Get current browser state
            browser_state_summary = await self.browser_session.get_state_summary(
                cache_clickable_elements_hashes=True
            )
            elements_text = (
                browser_state_summary.element_tree.clickable_elements_to_string(
                    include_attributes=self.settings.include_attributes
                )
            )

            scroll_markers = []
            if (browser_state_summary.pixels_above or 0) > 0:
                scroll_markers.append(
                    f"↑ {browser_state_summary.pixels_above}px scrollable content ↑"
                )
            if (browser_state_summary.pixels_below or 0) > 0:
                scroll_markers.append(
                    f"↓ {browser_state_summary.pixels_below}px scrollable content ↓"
                )

            step_messages = []
            for idx, history_item in enumerate(
                self.state.history.history[self.task_start_step :]
            ):
                if not hasattr(history_item.model_output, "current_state"):
                    continue

                agent_output = history_item.model_output.current_state
                agent_result = history_item.result[-1] if history_item.result else None

                step_info = f"""
    === Step {idx + self.task_start_step} ===
    memory: {agent_output.memory}
    evaluation_previous_goal: {agent_output.evaluation_previous_goal}
    next_goal: {agent_output.next_goal}
    result from extracted_content: {agent_result.extracted_content if agent_result else "N/A"}
    """
                step_messages.append(step_info.strip())

            def format_tabs(tabs):
                return " ".join([f" {tab.title} ({tab.url})" for tab in tabs])

            self.current_message = f"""
    Here is the state information from your managed agent '{self.name}':
    {chr(10).join(step_messages)}

    === Browser State Snapshot ===
    [NAVIGATION CONTEXT]
    · Current URL: {browser_state_summary.url}
    · Page Title: {browser_state_summary.title}

    [TAB MANAGEMENT]
    Active Tab:
    {format_tabs(browser_state_summary.tabs)}

    [INTERACTIVE ELEMENTS]
    {" ".join(scroll_markers)}
    {elements_text if elements_text else "· No visible interactive elements"}""".strip()

        except Exception as e:
            logger.error(f"Error updating message: {e}")
            self.current_message = f"Error retrieving browser state: {str(e)}"

        return self.current_message

    def _too_many_failures(self) -> bool:
        """Check if we should stop due to too many failures"""
        if self.state.consecutive_failures >= self.settings.max_failures:
            logger.error(
                f"❌ Stopping due to {self.settings.max_failures} consecutive failures"
            )
            return True
        return False

    async def _handle_control_flags(self) -> bool:
        """Handle pause and stop flags. Returns True if execution should continue."""
        if self.state.stopped:
            logger.info("Agent stopped")
            return False

        while self.state.paused:
            await asyncio.sleep(0.2)  # Small delay to prevent CPU spinning
            if self.state.stopped:  # Allow stopping while paused
                return False
        return True

    def clear_memory(self):
        self.state.message_manager_state.history.messages = (
            self.state.message_manager_state.history.messages[:6]
        )

    async def task_summary(self, task: str = "") -> Dict[str, Any]:
        """Generate task summary by asking LLM to analyze the task execution history"""
        try:

            task_description = task or self.task
            

            summary_prompt = f"""
Based on the task execution history above, please provide a concise summary of what was accomplished for the task: "{task_description}"

Please respond with a brief summary focusing on:
1. What was the main result or outcome of the task
2. Any information that was extracted or actions that were completed
3. Current status (completed, failed, or in progress)

Keep the response concise and focused on the actual results."""

            # Use LLM to generate summary
            from langchain_core.messages import HumanMessage

            summary_messages = HumanMessage(content=summary_prompt)
            self._message_manager._add_message_with_tokens(summary_messages)
            input_messages = self._message_manager.get_messages()
            input_messages = self._convert_input_messages(input_messages)

            llm_response = await self.llm.ainvoke(input_messages)
            task_result = llm_response.content.strip()
            return task_result

        except Exception as e:
            logger.error(f"Error generating task summary: {e}")
            return {
                "task_result": f"Error completing task: {str(e)}",
                "browser_state": {"error": "Failed to retrieve browser state"}
            }

    async def excute_task(self, max_steps=30, task="") -> Dict[str, Any]:
        """Execute the task with enhanced logging and control flow from old browser_use agent"""
        self.update_browser_log_path()
        self.task_start_step = len(self.state.history.history)
        if task:
            self.clear_memory()
            self.add_new_task(task)
        try:
            for step in range(max_steps):
                if self._too_many_failures():
                    break

                # Check control flags before each step
                if not await self._handle_control_flags():
                    break

                await self.step()
                task_is_done = self.state.history.is_done()
                if task_is_done:
                    if self.settings.validate_output and step < max_steps - 1:
                        if not await self._validate_output():
                            continue

                    logger.info("✅ Task completed successfully")
                    if self.register_done_callback:
                        if asyncio.iscoroutinefunction(self.register_done_callback):
                            await self.register_done_callback(self.state.history)
                        else:
                            self.register_done_callback(self.state.history)
                    break
            else:
                logger.info("❌ Failed to complete task in maximum steps")

        finally:
            # await self._update_message()

        # Generate and return task summary
            return await self.task_summary(task)

    def __call__(self, state, task: str, **kwargs) -> Dict[str, Any]:
        """Adds additional prompting for the managed agent, runs it, and wraps the output.
        This method is called only by a managed agent.
        Returns the task summary dictionary.
        """
        # kwargs reserved for future extensibility
        task_summary = self.loop.run_until_complete(self.excute_task(task=task))
        log_path = f"{self.log_dir}/log_browser_use_{self.current_log_idx - 1}.log"  # Use previous index since it was incremented
        state["_print_outputs"] += (
            f"\n The log can be found in {log_path} \n ========================== \n The result from the browseruse_agent: {task_summary}"
            + "\n"
        )
