import asyncio
import enum
import json
import logging
import re
import cv2
import base64
from io import BytesIO
from PIL import Image
import random
import requests
from typing import Generic, TypeVar, cast

from langchain_core.language_models.chat_models import BaseChatModel
from playwright.async_api import Element<PERSON><PERSON><PERSON>, Page
from crawl4ai import As<PERSON><PERSON><PERSON><PERSON>raw<PERSON>, CrawlerRunConfig
from crawl4ai.markdown_generation_strategy import De<PERSON>ultM<PERSON>downGenerator

# from lmnr.sdk.laminar import Laminar
from pydantic import BaseModel

from anotherme.browser_use.agent.views import ActionModel, ActionResult
from anotherme.browser_use.browser import BrowserSession
from anotherme.browser_use.controller.registry.service import Registry
from anotherme.browser_use.controller.views import (
    ClickElementAction,
    CloseTabAction,
    DoneAction,
    DragDropAction,
    GoToUrlAction,
    InputTextAction,
    NoParamsAction,
    OpenTabAction,
    Position,
    ScrollAction,
    SearchGoogleAction,
    SendKeysAction,
    SwitchTabAction,
    DownloadImageAction,
    UploadFileAction,
    Search1688Action,
)
from anotherme.browser_use.utils import time_execution_sync
from anotherme.tools.rpa_imagesearch_1688 import search_1688

logger = logging.getLogger(__name__)


Context = TypeVar("Context")


class Controller(Generic[Context]):
    def __init__(
        self,
        exclude_actions: list[str] = [],
        output_model: type[BaseModel] | None = None,
    ):
        self.registry = Registry[Context](exclude_actions)

        """Register all default browser actions"""

        if output_model is not None:
            # Create a new model that extends the output model with success parameter
            class ExtendedOutputModel(BaseModel):  # type: ignore
                success: bool = True
                data: output_model  # type: ignore

            @self.registry.action(
                "Complete task - with return text and if the task is finished (success=True) or not yet  completely finished (success=False), because last step is reached",
                param_model=ExtendedOutputModel,
            )
            async def done(params: ExtendedOutputModel):
                # Exclude success from the output JSON since it's an internal parameter
                output_dict = params.data.model_dump()

                # Enums are not serializable, convert to string
                for key, value in output_dict.items():
                    if isinstance(value, enum.Enum):
                        output_dict[key] = value.value

                return ActionResult(
                    is_done=True,
                    success=params.success,
                    extracted_content=json.dumps(output_dict),
                )
        else:

            @self.registry.action(
                "Complete task - with return text and if the task is finished (success=True) or not yet  completely finished (success=False), because last step is reached",
                param_model=DoneAction,
            )
            async def done(params: DoneAction):
                return ActionResult(
                    is_done=True, success=params.success, extracted_content=params.text
                )

        # Basic Navigation Actions
        @self.registry.action(
            "Search the query in Google, the query should be a search query like humans search in Google, concrete and not vague or super long.",
            param_model=SearchGoogleAction,
        )
        async def search_google(
            params: SearchGoogleAction, browser_session: BrowserSession
        ):
            search_url = f"https://www.google.com/search?q={params.query}&udm=14"

            page = await browser_session.get_current_page()
            if page.url in ("about:blank", "https://www.google.com"):
                await page.goto(search_url)
                await page.wait_for_load_state()
            else:
                page = await browser_session.create_new_tab(search_url)

            msg = f'🔍  Searched for "{params.query}" in Google'
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        @self.registry.action(
            "Navigate to URL in the current tab", param_model=GoToUrlAction
        )
        async def go_to_url(params: GoToUrlAction, browser_session: BrowserSession):
            page = await browser_session.get_current_page()
            if page:
                await page.goto(params.url, timeout=60000)
                await page.wait_for_load_state()
            else:
                page = await browser_session.create_new_tab(params.url)
            msg = f"🔗  Navigated to {params.url}"
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        @self.registry.action("Go back", param_model=NoParamsAction)
        async def go_back(params: NoParamsAction, browser_session: BrowserSession):
            await browser_session.go_back()
            msg = "🔙  Navigated back"
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        # wait for x seconds
        @self.registry.action("Wait for x seconds default 3")
        async def wait(seconds: int = 3):
            msg = f"🕒  Waiting for {seconds} seconds"
            logger.info(msg)
            await asyncio.sleep(seconds)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        # Element Interaction Actions
        @self.registry.action("Click element by index", param_model=ClickElementAction)
        async def click_element_by_index(
            params: ClickElementAction, browser_session: BrowserSession
        ):
            # Browser is now a BrowserSession itself

            if params.index not in await browser_session.get_selector_map():
                raise Exception(
                    f"Element with index {params.index} does not exist - retry or use alternative actions"
                )

            element_node = await browser_session.get_dom_element_by_index(params.index)
            initial_pages = len(browser_session.tabs)

            # if element has file uploader then dont click
            if (
                await browser_session.find_file_upload_element_by_index(params.index)
                is not None
            ):
                msg = f"Index {params.index} - has an element which opens file upload dialog. To upload files please use a specific function to upload files "
                logger.info(msg)
                return ActionResult(extracted_content=msg, include_in_memory=True)

            msg = None

            try:
                download_path = await browser_session._click_element_node(element_node)
                if download_path:
                    msg = f"💾  Downloaded file to {download_path}"
                else:
                    msg = f"🖱️  Clicked button with index {params.index}: {element_node.get_all_text_till_next_clickable_element(max_depth=2)}"

                logger.info(msg)
                logger.debug(f"Element xpath: {element_node.xpath}")
                if len(browser_session.tabs) > initial_pages:
                    new_tab_msg = "New tab opened - switching to it"
                    msg += f" - {new_tab_msg}"
                    logger.info(new_tab_msg)
                    await browser_session.switch_to_tab(-1)
                return ActionResult(extracted_content=msg, include_in_memory=True)
            except Exception as e:
                logger.warning(
                    f"Element not clickable with index {params.index} - most likely the page changed"
                )
                return ActionResult(error=str(e))

        @self.registry.action(
            "Input text into a input interactive element",
            param_model=InputTextAction,
        )
        async def input_text(
            params: InputTextAction,
            browser_session: BrowserSession,
            has_sensitive_data: bool = False,
        ):
            if params.index not in await browser_session.get_selector_map():
                raise Exception(
                    f"Element index {params.index} does not exist - retry or use alternative actions"
                )

            element_node = await browser_session.get_dom_element_by_index(params.index)
            await browser_session._input_text_element_node(element_node, params.text)
            if not has_sensitive_data:
                msg = f"⌨️  Input {params.text} into index {params.index}"
            else:
                msg = f"⌨️  Input sensitive data into index {params.index}"
            logger.info(msg)
            logger.debug(f"Element xpath: {element_node.xpath}")
            return ActionResult(extracted_content=msg, include_in_memory=True)

        # Save PDF
        @self.registry.action("Save the current page as a PDF file")
        async def save_pdf(browser_session: BrowserSession):
            page = await browser_session.get_current_page()
            short_url = re.sub(r"^https?://(?:www\.)?|/$", "", page.url)
            slug = re.sub(r"[^a-zA-Z0-9]+", "-", short_url).strip("-").lower()
            sanitized_filename = f"{slug}.pdf"

            await page.emulate_media(media="screen")
            await page.pdf(path=sanitized_filename, format="A4", print_background=False)
            msg = f"Saving page with URL {page.url} as PDF to ./{sanitized_filename}"
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        # Tab Management Actions
        @self.registry.action("Switch tab", param_model=SwitchTabAction)
        async def switch_tab(params: SwitchTabAction, browser_session: BrowserSession):
            await browser_session.switch_to_tab(params.page_id)
            # Wait for tab to be ready and ensure references are synchronized
            page = await browser_session.get_current_page()
            await page.wait_for_load_state()
            msg = f"🔄  Switched to tab {params.page_id}"
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        @self.registry.action(
            "Open a specific url in new tab", param_model=OpenTabAction
        )
        async def open_tab(params: OpenTabAction, browser_session: BrowserSession):
            await browser_session.create_new_tab(params.url)
            msg = f"🔗  Opened new tab with {params.url}"
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        @self.registry.action("Close an existing tab", param_model=CloseTabAction)
        async def close_tab(params: CloseTabAction, browser_session: BrowserSession):
            await browser_session.switch_to_tab(params.page_id)
            page = await browser_session.get_current_page()
            url = page.url
            await page.close()
            new_page = await browser_session.get_current_page()
            new_page_idx = browser_session.tabs.index(new_page)
            msg = f"❌  Closed tab #{params.page_id} with {url}, now focused on tab #{new_page_idx} with url {new_page.url}"
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        # Content Actions
        # @self.registry.action(
        #     "Extract page content to retrieve specific information from the page, e.g. all company names, a specific description, all information about xyc, 4 links with companies in structured format. Use include_links true if the goal requires links",
        # )
        # async def extract_content(
        #     goal: str,
        #     browser_session: BrowserSession,
        #     page_extraction_llm: BaseChatModel,
        #     include_links: bool = False,
        # ):
        #     page = await browser_session.get_current_page()
        #     import markdownify

        #     strip = []
        #     if not include_links:
        #         strip = ["a", "img"]

        #     content = markdownify.markdownify(await page.content(), strip=strip)

        #     # manually append iframe text into the content so it's readable by the LLM (includes cross-origin iframes)
        #     for iframe in page.frames:
        #         if iframe.url != page.url and not iframe.url.startswith("data:"):
        #             content += f"\n\nIFRAME {iframe.url}:\n"
        #             content += markdownify.markdownify(await iframe.content())

        #     prompt = "Your task is to extract the content of the page. You will be given a page and a goal and you should extract all relevant information around this goal from the page. If the goal is vague, summarize the page. Respond in json format. Extraction goal: {goal}, Page: {page}"
        #     template = PromptTemplate(input_variables=["goal", "page"], template=prompt)
        #     try:
        #         output = await page_extraction_llm.ainvoke(
        #             template.format(goal=goal, page=content)
        #         )
        #         msg = f"📄  Extracted from page\n: {output.content}\n"
        #         logger.info(msg)
        #         return ActionResult(extracted_content=msg, include_in_memory=True)
        #     except Exception as e:
        #         logger.debug(f"Error extracting content: {e}")
        #         msg = f"📄  Extracted from page\n: {content}\n"
        #         logger.info(msg)
        #         return ActionResult(extracted_content=msg)

        # Content Actions
        @self.registry.action(
            """Extract and analyze webpage content using text, images or videos.
            Args:
                prompt: Query or instruction for analyzing the webpage content
                web_text: Whether to extract and analyze text content (default: True)
                web_image: Whether to extract and analyze images (default: False)
                web_video: Whether to extract and analyze videos (default: False)
                choices: Optional list of predefined response options to choose from (default: False)
            """,
        )
        async def extract_content(
            prompt: str,
            browser_session: BrowserSession,
            page_extraction_llm: BaseChatModel,
            web_text: bool = True,
            web_image: bool = False,
            web_video: bool = False,
            choices: list[str] | None = None,
        ):
            if not (web_text or web_image or web_video):
                return ActionResult(
                    "At least one of web_text, web_image, or web_video, which is the type of the content you want to extract, must be True"
                )

            page = await browser_session.get_current_page()
            async with AsyncWebCrawler() as crawler:
                md_generator = DefaultMarkdownGenerator(
                    options={
                        "ignore_links": True,
                    }
                )
                crawler_cfg = CrawlerRunConfig(
                    magic=True,
                    simulate_user=True,
                    override_navigator=True,
                    markdown_generator=md_generator,
                )

                raw_html = await page.content()
                result = await crawler.arun(
                    url=f"raw:{raw_html}",
                    config=crawler_cfg,
                )

            modified_markdown = re.sub(
                r"\[(.*?)\]\(.*?\)", r"[\1](a link)", result.markdown
            )
            result.markdown = modified_markdown

            if choices:
                prompt = f"Analyze the content of the webpage, {prompt}, your response can only be selected from the following options: {choices}, do not output any other content."
            else:
                prompt = f"Analyze the content of the webpage, {prompt}."

            if web_text:
                prompt += (
                    f"\n Here is the text content within the webpage: {result.markdown}"
                )

            content = [{"type": "text", "text": prompt}]

            if web_image:
                images_dict = {}
                for image_info in result.media.get("images", []):
                    group_id = image_info["group_id"]
                    if group_id not in images_dict:
                        images_dict[group_id] = []
                    images_dict[group_id].append(image_info)

                # Filter images to get the one with maximum width for each group
                filtered_images = {}
                for group_id, images in images_dict.items():
                    # Filter out images where width is None, width < 256, or src is None
                    valid_images = [
                        img
                        for img in images
                        if img.get("width") is not None
                        and img.get("width") >= 256
                        and img.get("src") is not None
                    ]
                    if valid_images:
                        # Find the smallest image among valid ones
                        smallest_image = min(valid_images, key=lambda x: x["width"])
                        filtered_images[group_id] = smallest_image

                # Replace the original images_dict with filtered results
                images_dict = filtered_images

                # Randomly select 3 images from the first 15 images
                all_images = list(images_dict.values())
                first_15_images = all_images[:15]  # Get first 15 images
                selected_images = random.sample(
                    first_15_images, min(3, len(first_15_images))
                )

                # Download and encode images
                encoded_images = []
                for img in selected_images:
                    encoded_images.append(
                        {
                            "type": "image_url",
                            "image_url": {"url": f"https:{img.get('src')}"},
                        }
                    )
                    print(f"Successfully encoded image: {img.get('src')}")

                if len(encoded_images) > 0:
                    image_prompt = "\n Here is the images within the webpage: "
                    content.append({"type": "text", "text": image_prompt})
                    content.extend(encoded_images)

            if web_video:
                video_list = [
                    video_info
                    for video_info in result.media.get("videos", [])
                    if video_info.get("src") is not None
                ]
                video_list = video_list[:3]

                # Extract frames from each video
                for i, video_info in enumerate(video_list):
                    video_src = video_info.get("src")

                    # Ensure the URL is complete
                    if video_src.startswith("//"):
                        video_url = f"https:{video_src}"
                    else:
                        video_url = video_src

                    print(f"Extracting frames from video: {video_url}")
                    # st = time.time()
                    frames = await extract_video_frames(video_url, num_frames=3)
                    # print(f'using time:{time.time() - st}')

                    if len(frames) > 0:
                        print(f"Successfully extracted {len(frames)} frames from video")
                    else:
                        print(f"Failed to extract frames from video: {video_url}")

                    # Update content with video frames
                    video_prompt = f"\n Here are a few frames extracted from the {i}th video on the webpage: "
                    content.append({"type": "text", "text": video_prompt})
                    content.extend(frames)

            # Create messages with both text and images
            messages = [{"role": "user", "content": content}]

            try:
                output = await page_extraction_llm.ainvoke(messages)
                msg = f"📄  Results from page\n: {output.content}\n"
                logger.info(msg)
                return ActionResult(extracted_content=msg, include_in_memory=True)
            except Exception as e:
                logger.debug(f"Error extracting content: {e}")
                msg = f"📄  Extracted from page\n: {content}\n"
                logger.info(msg)
                return ActionResult(extracted_content=msg)

        @self.registry.action(
            """Download image from a URL or from an element on the page by index.

            This function can:
            1. Download an image directly from a URL
            2. Take a screenshot of a webpage at a given URL
            3. Download an image from an element on the current page
            4. Take a screenshot of an element on the current page
            5. Take a screenshot of the entire current page

            Args:
                save_path: Required path of image (ie. ./image.png) where the image will be saved
                url: Optional URL of an image to download or webpage to screenshot
                element_index: Optional index of an element to screenshot or download
                full_page: Whether to capture the full page when taking a screenshot
            """,
            param_model=DownloadImageAction,
        )
        async def download_image(
            params: DownloadImageAction, browser_session: BrowserSession
        ):
            page = await browser_session.get_current_page()
            save_path = params.save_path

            # Download image directly from URL
            if params.url:
                if params.url.lower().endswith(
                    (".png", ".jpg", ".jpeg", ".webp", ".gif")
                ):
                    # Use requests to download image directly
                    response = await asyncio.to_thread(requests.get, params.url)
                    with open(save_path, "wb") as f:
                        f.write(response.content)
                    msg = f"📷 Downloaded image from URL to {save_path}"
                else:
                    # If not a direct image URL, open new tab and take screenshot
                    new_page = await browser_session.create_new_tab(params.url)
                    await new_page.wait_for_load_state()
                    await new_page.screenshot(path=save_path, full_page=False)
                    msg = f"📷 Took screenshot of page {params.url} and saved to {save_path}"

            # Download image through element index
            elif params.element_index is not None:
                if params.element_index not in await browser_session.get_selector_map():
                    raise Exception(
                        f"Element with index {params.element_index} does not exist - retry or use alternative actions"
                    )

                element_node = await browser_session.get_dom_element_by_index(
                    params.element_index
                )

                # Check if it's an image element
                if element_node.tag_name.lower() == "img":
                    # Get image URL and download
                    img_src = element_node.attributes.get("src")
                    if img_src:
                        response = await asyncio.to_thread(requests.get, img_src)
                        with open(save_path, "wb") as f:
                            f.write(response.content)
                        msg = f"📷 Downloaded image from element {params.element_index} to {save_path}"
                    else:
                        # If no src attribute, take screenshot of element directly
                        element_handle = await browser_session.get_locate_element(
                            element_node
                        )
                        await element_handle.screenshot(path=save_path)
                        msg = f"📷 Took screenshot of element {params.element_index} and saved to {save_path}"
                else:
                    # For non-image element, take screenshot directly
                    element_handle = await browser_session.get_locate_element(
                        element_node
                    )
                    await element_handle.screenshot(path=save_path)
                    msg = f"📷 Took screenshot of element {params.element_index} and saved to {save_path}"

            # If no URL or element index provided, take screenshot of entire page
            else:
                await page.screenshot(path=save_path, full_page=params.full_page)
                msg = f"📷 Took screenshot of current page and saved to {save_path}"

            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        @self.registry.action(
            """Upload a file to a file input element on the page.

            This function allows you to upload a file to a file input element identified by its index.
            It works with standard HTML file input elements (<input type="file">).

            Args:
                element_index: Index of the file input element where the file will be uploaded
                file_path: Path to the file that will be uploaded (can be absolute or relative)
                multiple_files: Whether to treat file_path as a comma-separated list of multiple files
            """,
            param_model=UploadFileAction,
        )
        async def upload_file(
            params: UploadFileAction, browser_session: BrowserSession
        ):
            page = await browser_session.get_current_page()

            # 检查元素是否存在
            if params.element_index not in await browser_session.get_selector_map():
                raise Exception(
                    f"Element with index {params.element_index} does not exist - retry or use alternative actions"
                )

            # 获取元素节点
            element_node = await browser_session.get_dom_element_by_index(
                params.element_index
            )

            # 处理文件路径（支持多文件上传）
            if params.multiple_files:
                file_paths = [path.strip() for path in params.file_path.split(",")]
            else:
                file_paths = params.file_path

            try:
                # 获取元素句柄
                element_handle = await browser_session.get_locate_element(element_node)

                # 尝试直接设置文件
                try:
                    await element_handle.set_input_files(file_paths)
                    msg = f"📎 Uploaded file(s) {params.file_path} to element {params.element_index}"
                except Exception as e:
                    # 如果直接设置失败，尝试使用文件选择器
                    logger.info(
                        f"Direct file upload failed, trying with file chooser: {str(e)}"
                    )

                    # 使用文件选择器方法
                    with page.expect_file_chooser() as fc_info:
                        await element_handle.click()

                    file_chooser = await fc_info.value
                    await file_chooser.set_files(file_paths)
                    msg = f"📎 Uploaded file(s) {params.file_path} to element {params.element_index} using file chooser"

                logger.info(msg)
                return ActionResult(extracted_content=msg, include_in_memory=True)

            except Exception as e:
                error_msg = f"Failed to upload file: {str(e)}"
                logger.error(error_msg)
                return ActionResult(error=error_msg)

        @self.registry.action(
            'Get the accessibility tree of the page in the format "role name" with the number_of_elements to return',
        )
        async def get_ax_tree(number_of_elements: int, browser_session: BrowserSession):
            page = await browser_session.get_current_page()
            node = await page.accessibility.snapshot(interesting_only=True)

            def flatten_ax_tree(node, lines):
                if not node:
                    return
                role = node.get("role", "")
                name = node.get("name", "")
                lines.append(f"{role} {name}")
                for child in node.get("children", []):
                    flatten_ax_tree(child, lines)

            lines = []
            flatten_ax_tree(node, lines)
            msg = "\n".join(lines)
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=False)

        @self.registry.action(
            "Scroll down the page by pixel amount - if none is given, scroll one page",
            param_model=ScrollAction,
        )
        async def scroll_down(params: ScrollAction, browser_session: BrowserSession):
            """
            (a) Use browser._scroll_container for container-aware scrolling.
            (b) If that JavaScript throws, fall back to window.scrollBy().
            """
            page = await browser_session.get_current_page()
            dy = params.amount or await page.evaluate("() => window.innerHeight")

            try:
                await browser_session._scroll_container(dy)
            except Exception as e:
                # Hard fallback: always works on root scroller
                await page.evaluate("(y) => window.scrollBy(0, y)", dy)
                logger.debug(
                    "Smart scroll failed; used window.scrollBy fallback", exc_info=e
                )

            amount_str = (
                f"{params.amount} pixels" if params.amount is not None else "one page"
            )
            msg = f"🔍 Scrolled down the page by {amount_str}"
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        @self.registry.action(
            "Scroll up the page by pixel amount - if none is given, scroll one page",
            param_model=ScrollAction,
        )
        async def scroll_up(params: ScrollAction, browser_session: BrowserSession):
            page = await browser_session.get_current_page()
            dy = -(params.amount or await page.evaluate("() => window.innerHeight"))

            try:
                await browser_session._scroll_container(dy)
            except Exception as e:
                await page.evaluate("(y) => window.scrollBy(0, y)", dy)
                logger.debug(
                    "Smart scroll failed; used window.scrollBy fallback", exc_info=e
                )

            amount_str = (
                f"{params.amount} pixels" if params.amount is not None else "one page"
            )
            msg = f"🔍 Scrolled up the page by {amount_str}"
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        # send keys
        @self.registry.action(
            "Send strings of special keys like Escape,Backspace, Insert, PageDown, Delete, Enter, Shortcuts such as `Control+o`, `Control+Shift+T` are supported as well. This gets used in keyboard.press. ",
            param_model=SendKeysAction,
        )
        async def send_keys(params: SendKeysAction, browser_session: BrowserSession):
            page = await browser_session.get_current_page()

            try:
                await page.keyboard.press(params.keys)
            except Exception as e:
                if "Unknown key" in str(e):
                    # loop over the keys and try to send each one
                    for key in params.keys:
                        try:
                            await page.keyboard.press(key)
                        except Exception as e:
                            logger.debug(f"Error sending key {key}: {str(e)}")
                            raise e
                else:
                    raise e
            msg = f"⌨️  Sent keys: {params.keys}"
            logger.info(msg)
            return ActionResult(extracted_content=msg, include_in_memory=True)

        @self.registry.action(
            description="If you dont find something which you want to interact with, scroll to it",
        )
        async def scroll_to_text(text: str, browser_session: BrowserSession):  # type: ignore
            page = await browser_session.get_current_page()
            try:
                # Try different locator strategies
                locators = [
                    page.get_by_text(text, exact=False),
                    page.locator(f"text={text}"),
                    page.locator(f"//*[contains(text(), '{text}')]"),
                ]

                for locator in locators:
                    try:
                        if await locator.count() == 0:
                            continue

                        element = locator.first
                        is_visible = await element.is_visible()
                        bbox = await element.bounding_box()

                        if (
                            is_visible
                            and bbox is not None
                            and bbox["width"] > 0
                            and bbox["height"] > 0
                        ):
                            await element.scroll_into_view_if_needed()
                            await asyncio.sleep(0.5)  # Wait for scroll to complete
                            msg = f"🔍  Scrolled to text: {text}"
                            logger.info(msg)
                            return ActionResult(
                                extracted_content=msg, include_in_memory=True
                            )

                    except Exception as e:
                        logger.debug(f"Locator attempt failed: {str(e)}")
                        continue

                msg = f"Text '{text}' not found or not visible on page"
                logger.info(msg)
                return ActionResult(extracted_content=msg, include_in_memory=True)

            except Exception as e:
                msg = f"Failed to scroll to text '{text}': {str(e)}"
                logger.error(msg)
                return ActionResult(error=msg, include_in_memory=True)

        @self.registry.action(
            description="Get all options from a native dropdown",
        )
        async def get_dropdown_options(
            index: int, browser_session: BrowserSession
        ) -> ActionResult:
            """Get all options from a native dropdown"""
            page = await browser_session.get_current_page()
            selector_map = await browser_session.get_selector_map()
            dom_element = selector_map[index]

            try:
                # Frame-aware approach since we know it works
                all_options = []
                frame_index = 0

                for frame in page.frames:
                    try:
                        options = await frame.evaluate(
                            """
							(xpath) => {
								const select = document.evaluate(xpath, document, null,
									XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
								if (!select) return null;

								return {
									options: Array.from(select.options).map(opt => ({
										text: opt.text, //do not trim, because we are doing exact match in select_dropdown_option
										value: opt.value,
										index: opt.index
									})),
									id: select.id,
									name: select.name
								};
							}
						""",
                            dom_element.xpath,
                        )

                        if options:
                            logger.debug(f"Found dropdown in frame {frame_index}")
                            logger.debug(
                                f"Dropdown ID: {options['id']}, Name: {options['name']}"
                            )

                            formatted_options = []
                            for opt in options["options"]:
                                # encoding ensures AI uses the exact string in select_dropdown_option
                                encoded_text = json.dumps(opt["text"])
                                formatted_options.append(
                                    f"{opt['index']}: text={encoded_text}"
                                )

                            all_options.extend(formatted_options)

                    except Exception as frame_e:
                        logger.debug(
                            f"Frame {frame_index} evaluation failed: {str(frame_e)}"
                        )

                    frame_index += 1

                if all_options:
                    msg = "\n".join(all_options)
                    msg += "\nUse the exact text string in select_dropdown_option"
                    logger.info(msg)
                    return ActionResult(extracted_content=msg, include_in_memory=True)
                else:
                    msg = "No options found in any frame for dropdown"
                    logger.info(msg)
                    return ActionResult(extracted_content=msg, include_in_memory=True)

            except Exception as e:
                logger.error(f"Failed to get dropdown options: {str(e)}")
                msg = f"Error getting options: {str(e)}"
                logger.info(msg)
                return ActionResult(extracted_content=msg, include_in_memory=True)

        @self.registry.action(
            description="Select dropdown option for interactive element index by the text of the option you want to select",
        )
        async def select_dropdown_option(
            index: int,
            text: str,
            browser_session: BrowserSession,
        ) -> ActionResult:
            """Select dropdown option by the text of the option you want to select"""
            page = await browser_session.get_current_page()
            selector_map = await browser_session.get_selector_map()
            dom_element = selector_map[index]

            # Validate that we're working with a select element
            if dom_element.tag_name != "select":
                logger.error(
                    f"Element is not a select! Tag: {dom_element.tag_name}, Attributes: {dom_element.attributes}"
                )
                msg = f"Cannot select option: Element with index {index} is a {dom_element.tag_name}, not a select"
                return ActionResult(extracted_content=msg, include_in_memory=True)

            logger.debug(
                f"Attempting to select '{text}' using xpath: {dom_element.xpath}"
            )
            logger.debug(f"Element attributes: {dom_element.attributes}")
            logger.debug(f"Element tag: {dom_element.tag_name}")

            # xpath = "//" + dom_element.xpath

            try:
                frame_index = 0
                for frame in page.frames:
                    try:
                        logger.debug(f"Trying frame {frame_index} URL: {frame.url}")

                        # First verify we can find the dropdown in this frame
                        find_dropdown_js = """
							(xpath) => {
								try {
									const select = document.evaluate(xpath, document, null,
										XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
									if (!select) return null;
									if (select.tagName.toLowerCase() !== 'select') {
										return {
											error: `Found element but it's a ${select.tagName}, not a SELECT`,
											found: false
										};
									}
									return {
										id: select.id,
										name: select.name,
										found: true,
										tagName: select.tagName,
										optionCount: select.options.length,
										currentValue: select.value,
										availableOptions: Array.from(select.options).map(o => o.text.trim())
									};
								} catch (e) {
									return {error: e.toString(), found: false};
								}
							}
						"""

                        dropdown_info = await frame.evaluate(
                            find_dropdown_js, dom_element.xpath
                        )

                        if dropdown_info:
                            if not dropdown_info.get("found"):
                                logger.error(
                                    f"Frame {frame_index} error: {dropdown_info.get('error')}"
                                )
                                continue

                            logger.debug(
                                f"Found dropdown in frame {frame_index}: {dropdown_info}"
                            )

                            # "label" because we are selecting by text
                            # nth(0) to disable error thrown by strict mode
                            # timeout=1000 because we are already waiting for all network events, therefore ideally we don't need to wait a lot here (default 30s)
                            selected_option_values = (
                                await frame.locator("//" + dom_element.xpath)
                                .nth(0)
                                .select_option(label=text, timeout=1000)
                            )

                            msg = f"selected option {text} with value {selected_option_values}"
                            logger.info(msg + f" in frame {frame_index}")

                            return ActionResult(
                                extracted_content=msg, include_in_memory=True
                            )

                    except Exception as frame_e:
                        logger.error(
                            f"Frame {frame_index} attempt failed: {str(frame_e)}"
                        )
                        logger.error(f"Frame type: {type(frame)}")
                        logger.error(f"Frame URL: {frame.url}")

                    frame_index += 1

                msg = f"Could not select option '{text}' in any frame"
                logger.info(msg)
                return ActionResult(extracted_content=msg, include_in_memory=True)

            except Exception as e:
                msg = f"Selection failed: {str(e)}"
                logger.error(msg)
                return ActionResult(error=msg, include_in_memory=True)

        @self.registry.action(
            "Drag and drop elements or between coordinates on the page - useful for canvas drawing, sortable lists, sliders, file uploads, and UI rearrangement",
            param_model=DragDropAction,
        )
        async def drag_drop(
            params: DragDropAction, browser_session: BrowserSession
        ) -> ActionResult:
            """
            Performs a precise drag and drop operation between elements or coordinates.
            """

            async def get_drag_elements(
                page: Page,
                source_selector: str,
                target_selector: str,
            ) -> tuple[ElementHandle | None, ElementHandle | None]:
                """Get source and target elements with appropriate error handling."""
                source_element = None
                target_element = None

                try:
                    # page.locator() auto-detects CSS and XPath
                    source_locator = page.locator(source_selector)
                    target_locator = page.locator(target_selector)

                    # Check if elements exist
                    source_count = await source_locator.count()
                    target_count = await target_locator.count()

                    if source_count > 0:
                        source_element = await source_locator.first.element_handle()
                        logger.debug(
                            f"Found source element with selector: {source_selector}"
                        )
                    else:
                        logger.warning(f"Source element not found: {source_selector}")

                    if target_count > 0:
                        target_element = await target_locator.first.element_handle()
                        logger.debug(
                            f"Found target element with selector: {target_selector}"
                        )
                    else:
                        logger.warning(f"Target element not found: {target_selector}")

                except Exception as e:
                    logger.error(f"Error finding elements: {str(e)}")

                return source_element, target_element

            async def get_element_coordinates(
                source_element: ElementHandle,
                target_element: ElementHandle,
                source_position: Position | None,
                target_position: Position | None,
            ) -> tuple[tuple[int, int] | None, tuple[int, int] | None]:
                """Get coordinates from elements with appropriate error handling."""
                source_coords = None
                target_coords = None

                try:
                    # Get source coordinates
                    if source_position:
                        source_coords = (source_position.x, source_position.y)
                    else:
                        source_box = await source_element.bounding_box()
                        if source_box:
                            source_coords = (
                                int(source_box["x"] + source_box["width"] / 2),
                                int(source_box["y"] + source_box["height"] / 2),
                            )

                    # Get target coordinates
                    if target_position:
                        target_coords = (target_position.x, target_position.y)
                    else:
                        target_box = await target_element.bounding_box()
                        if target_box:
                            target_coords = (
                                int(target_box["x"] + target_box["width"] / 2),
                                int(target_box["y"] + target_box["height"] / 2),
                            )
                except Exception as e:
                    logger.error(f"Error getting element coordinates: {str(e)}")

                return source_coords, target_coords

            async def execute_drag_operation(
                page: Page,
                source_x: int,
                source_y: int,
                target_x: int,
                target_y: int,
                steps: int,
                delay_ms: int,
            ) -> tuple[bool, str]:
                """Execute the drag operation with comprehensive error handling."""
                try:
                    # Try to move to source position
                    try:
                        await page.mouse.move(source_x, source_y)
                        logger.debug(
                            f"Moved to source position ({source_x}, {source_y})"
                        )
                    except Exception as e:
                        logger.error(f"Failed to move to source position: {str(e)}")
                        return False, f"Failed to move to source position: {str(e)}"

                    # Press mouse button down
                    await page.mouse.down()

                    # Move to target position with intermediate steps
                    for i in range(1, steps + 1):
                        ratio = i / steps
                        intermediate_x = int(source_x + (target_x - source_x) * ratio)
                        intermediate_y = int(source_y + (target_y - source_y) * ratio)

                        await page.mouse.move(intermediate_x, intermediate_y)

                        if delay_ms > 0:
                            await asyncio.sleep(delay_ms / 1000)

                    # Move to final target position
                    await page.mouse.move(target_x, target_y)

                    # Move again to ensure dragover events are properly triggered
                    await page.mouse.move(target_x, target_y)

                    # Release mouse button
                    await page.mouse.up()

                    return True, "Drag operation completed successfully"

                except Exception as e:
                    return False, f"Error during drag operation: {str(e)}"

            page = await browser_session.get_current_page()

            try:
                # Initialize variables
                source_x: int | None = None
                source_y: int | None = None
                target_x: int | None = None
                target_y: int | None = None

                # Normalize parameters
                steps = max(1, params.steps or 10)
                delay_ms = max(0, params.delay_ms or 5)

                # Case 1: Element selectors provided
                if params.element_source and params.element_target:
                    logger.debug("Using element-based approach with selectors")

                    source_element, target_element = await get_drag_elements(
                        page,
                        params.element_source,
                        params.element_target,
                    )

                    if not source_element or not target_element:
                        error_msg = f"Failed to find {'source' if not source_element else 'target'} element"
                        return ActionResult(error=error_msg, include_in_memory=True)

                    source_coords, target_coords = await get_element_coordinates(
                        source_element,
                        target_element,
                        params.element_source_offset,
                        params.element_target_offset,
                    )

                    if not source_coords or not target_coords:
                        error_msg = f"Failed to determine {'source' if not source_coords else 'target'} coordinates"
                        return ActionResult(error=error_msg, include_in_memory=True)

                    source_x, source_y = source_coords
                    target_x, target_y = target_coords

                # Case 2: Coordinates provided directly
                elif all(
                    coord is not None
                    for coord in [
                        params.coord_source_x,
                        params.coord_source_y,
                        params.coord_target_x,
                        params.coord_target_y,
                    ]
                ):
                    logger.debug("Using coordinate-based approach")
                    source_x = params.coord_source_x
                    source_y = params.coord_source_y
                    target_x = params.coord_target_x
                    target_y = params.coord_target_y
                else:
                    error_msg = "Must provide either source/target selectors or source/target coordinates"
                    return ActionResult(error=error_msg, include_in_memory=True)

                # Validate coordinates
                if any(
                    coord is None for coord in [source_x, source_y, target_x, target_y]
                ):
                    error_msg = "Failed to determine source or target coordinates"
                    return ActionResult(error=error_msg, include_in_memory=True)

                # Perform the drag operation
                success, message = await execute_drag_operation(
                    page,
                    cast(int, source_x),
                    cast(int, source_y),
                    cast(int, target_x),
                    cast(int, target_y),
                    steps,
                    delay_ms,
                )

                if not success:
                    logger.error(f"Drag operation failed: {message}")
                    return ActionResult(error=message, include_in_memory=True)

                # Create descriptive message
                if params.element_source and params.element_target:
                    msg = f"🖱️ Dragged element '{params.element_source}' to '{params.element_target}'"
                else:
                    msg = f"🖱️ Dragged from ({source_x}, {source_y}) to ({target_x}, {target_y})"

                logger.info(msg)
                return ActionResult(extracted_content=msg, include_in_memory=True)

            except Exception as e:
                error_msg = f"Failed to perform drag and drop: {str(e)}"
                logger.error(error_msg)
                return ActionResult(error=error_msg, include_in_memory=True)

        @self.registry.action(
            "Google Sheets: Get the contents of the entire sheet",
            domains=["https://docs.google.com"],
        )
        async def read_sheet_contents(browser_session: BrowserSession):
            page = await browser_session.get_current_page()

            # select all cells
            await page.keyboard.press("Enter")
            await page.keyboard.press("Escape")
            await page.keyboard.press("ControlOrMeta+A")
            await page.keyboard.press("ControlOrMeta+C")

            extracted_tsv = await page.evaluate("() => navigator.clipboard.readText()")
            return ActionResult(extracted_content=extracted_tsv, include_in_memory=True)

        @self.registry.action(
            "Google Sheets: Get the contents of a cell or range of cells",
            domains=["https://docs.google.com"],
        )
        async def read_cell_contents(
            browser_session: BrowserSession, cell_or_range: str
        ):
            page = await browser_session.get_current_page()

            await select_cell_or_range(browser_session, cell_or_range)

            await page.keyboard.press("ControlOrMeta+C")
            await asyncio.sleep(0.1)
            extracted_tsv = await page.evaluate("() => navigator.clipboard.readText()")
            return ActionResult(extracted_content=extracted_tsv, include_in_memory=True)

        @self.registry.action(
            "Google Sheets: Update the content of a cell or range of cells",
            domains=["https://docs.google.com"],
        )
        async def update_cell_contents(
            browser_session: BrowserSession, cell_or_range: str, new_contents_tsv: str
        ):
            page = await browser_session.get_current_page()

            await select_cell_or_range(browser_session, cell_or_range)

            # simulate paste event from clipboard with TSV content
            await page.evaluate(f"""
				const clipboardData = new DataTransfer();
				clipboardData.setData('text/plain', `{new_contents_tsv}`);
				document.activeElement.dispatchEvent(new ClipboardEvent('paste', {{clipboardData}}));
			""")

            return ActionResult(
                extracted_content=f"Updated cells: {cell_or_range} = {new_contents_tsv}",
                include_in_memory=False,
            )

        @self.registry.action(
            "Google Sheets: Clear whatever cells are currently selected",
            domains=["https://docs.google.com"],
        )
        async def clear_cell_contents(
            browser_session: BrowserSession, cell_or_range: str
        ):
            page = await browser_session.get_current_page()

            await select_cell_or_range(browser_session, cell_or_range)

            await page.keyboard.press("Backspace")
            return ActionResult(
                extracted_content=f"Cleared cells: {cell_or_range}",
                include_in_memory=False,
            )

        @self.registry.action(
            "Google Sheets: Select a specific cell or range of cells",
            domains=["https://docs.google.com"],
        )
        async def select_cell_or_range(
            browser_session: BrowserSession, cell_or_range: str
        ):
            page = await browser_session.get_current_page()

            await page.keyboard.press(
                "Enter"
            )  # make sure we dont delete current cell contents if we were last editing
            await page.keyboard.press(
                "Escape"
            )  # to clear current focus (otherwise select range popup is additive)
            await asyncio.sleep(0.1)
            await page.keyboard.press(
                "Home"
            )  # move cursor to the top left of the sheet first
            await page.keyboard.press("ArrowUp")
            await asyncio.sleep(0.1)
            await page.keyboard.press("Control+G")  # open the goto range popup
            await asyncio.sleep(0.2)
            await page.keyboard.type(cell_or_range, delay=0.05)
            await asyncio.sleep(0.2)
            await page.keyboard.press("Enter")
            await asyncio.sleep(0.2)
            await page.keyboard.press(
                "Escape"
            )  # to make sure the popup still closes in the case where the jump failed
            return ActionResult(
                extracted_content=f"Selected cells: {cell_or_range}",
                include_in_memory=False,
            )

        @self.registry.action(
            "Google Sheets: Fallback method to type text into (only one) currently selected cell",
            domains=["https://docs.google.com"],
        )
        async def fallback_input_into_single_selected_cell(
            browser_session: BrowserSession, text: str
        ):
            page = await browser_session.get_current_page()

            await page.keyboard.type(text, delay=0.1)
            await page.keyboard.press(
                "Enter"
            )  # make sure to commit the input so it doesn't get overwritten by the next action
            await page.keyboard.press("ArrowUp")
            return ActionResult(
                extracted_content=f"Inputted text {text}", include_in_memory=False
            )

        @self.registry.action(
            "Search by image on 1688 to determine if the given product in the image is being sold on 1688.",
            param_model=Search1688Action,
        )
        async def image_search1688(
            params: Search1688Action,
        ):
            results = await search_1688(params.image_url, headless=False)
            sold = "is" if results else "is not"
            return ActionResult(
                extracted_content=f"the given product in the image {sold} sold in 1688!",
                include_in_memory=True,
            )

    # Register ---------------------------------------------------------------

    def action(self, description: str, **kwargs):
        """Decorator for registering custom actions

        @param description: Describe the LLM what the function does (better description == better function calling)
        """
        return self.registry.action(description, **kwargs)

    # Act --------------------------------------------------------------------

    @time_execution_sync("--act")
    async def act(
        self,
        action: ActionModel,
        browser_session: BrowserSession,
        #
        page_extraction_llm: BaseChatModel | None = None,
        sensitive_data: dict[str, str] | None = None,
        available_file_paths: list[str] | None = None,
        #
        context: Context | None = None,
    ) -> ActionResult:
        """Execute an action"""

        for action_name, params in action.model_dump(exclude_unset=True).items():
            if params is not None:
                # with Laminar.start_as_current_span(
                # 	name=action_name,
                # 	input={
                # 		'action': action_name,
                # 		'params': params,
                # 	},
                # 	span_type='TOOL',
                # ):
                result = await self.registry.execute_action(
                    action_name=action_name,
                    params=params,
                    browser_session=browser_session,
                    page_extraction_llm=page_extraction_llm,
                    sensitive_data=sensitive_data,
                    available_file_paths=available_file_paths,
                    context=context,
                )

                # Laminar.set_span_output(result)

                if isinstance(result, str):
                    return ActionResult(extracted_content=result)
                elif isinstance(result, ActionResult):
                    return result
                elif result is None:
                    return ActionResult()
                else:
                    raise ValueError(
                        f"Invalid action result type: {type(result)} of {result}"
                    )
        return ActionResult()


async def extract_video_frames(video_url, num_frames=3):
    """Extract frames from video URL and return as base64 encoded images"""
    try:
        # Open video with OpenCV
        cap = cv2.VideoCapture(video_url)

        if not cap.isOpened():
            print(f"Error: Could not open video {video_url}")
            return []

        # Get video properties
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        # fps = cap.get(cv2.CAP_PROP_FPS)

        if total_frames < num_frames:
            frame_indices = list(range(total_frames))
        else:
            # Calculate frame indices to extract evenly distributed frames
            num_frames = num_frames + 2
            frame_indices = [
                int(i * total_frames / num_frames) for i in range(num_frames)
            ]
            frame_indices = frame_indices[1:-1]

        extracted_frames = []

        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()

            if ret:
                # Convert BGR to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # Convert to PIL Image
                pil_image = Image.fromarray(frame_rgb)

                # Convert to base64
                buffer = BytesIO()
                pil_image.save(buffer, format="JPEG", quality=85)
                img_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")

                extracted_frames.append(
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{img_base64}"},
                    }
                )

                print(f"Extracted frame {frame_idx} from video {video_url}")

        cap.release()

        return extracted_frames

    except Exception as e:
        print(f"Error extracting frames from video {video_url}: {str(e)}")
        return []


async def extract_webpage_images_sorted(
    page_content: str,
    min_width: int = 100,
    min_height: int = 100,
    max_images: int = None
) -> list[dict]:
    """
    Extract image URLs from webpage content and return them sorted by size (largest first).

    Args:
        page_content: Raw HTML content of the webpage
        min_width: Minimum width filter for images (default: 100)
        min_height: Minimum height filter for images (default: 100)
        max_images: Maximum number of images to return (default: None for all)

    Returns:
        List of dictionaries containing image information sorted by size:
        [
            {
                "src": "image_url",
                "width": 800,
                "height": 600,
                "size": 480000,  # width * height
                "alt": "alt_text",
                "group_id": "unique_id"
            },
            ...
        ]
    """
    try:
        async with AsyncWebCrawler() as crawler:
            md_generator = DefaultMarkdownGenerator(
                options={
                    "ignore_links": True,
                }
            )
            crawler_cfg = CrawlerRunConfig(
                magic=True,
                simulate_user=True,
                override_navigator=True,
                markdown_generator=md_generator,
            )

            result = await crawler.arun(
                url=f"raw:{page_content}",
                config=crawler_cfg,
            )

        # Extract and process images
        images_list = []
        for image_info in result.media.get("images", []):
            width = image_info.get("width")
            height = image_info.get("height")
            src = image_info.get("src")

            # Filter out invalid images
            if (width is not None and height is not None and src is not None and
                width >= min_width and height >= min_height):

                # Calculate image size (area)
                size = width * height

                # Ensure URL is complete
                if src.startswith("//"):
                    src = f"https:{src}"
                elif src.startswith("/"):
                    # This would need the base URL to be complete, for now keep as is
                    pass

                images_list.append({
                    "src": src,
                    "width": width,
                    "height": height,
                    "size": size,
                    "alt": image_info.get("alt", ""),
                    "group_id": image_info.get("group_id", "")
                })

        # Sort by size (largest first)
        images_list.sort(key=lambda x: x["size"], reverse=True)

        # Limit number of images if specified
        if max_images is not None:
            images_list = images_list[:max_images]

        return images_list

    except Exception as e:
        print(f"Error extracting images from webpage: {str(e)}")
        return []


async def extract_webpage_images_from_browser_session(
    browser_session,
    min_width: int = 100,
    min_height: int = 100,
    max_images: int = None
) -> list[dict]:
    """
    Extract image URLs from current page in browser session and return them sorted by size.

    Args:
        browser_session: BrowserSession object
        min_width: Minimum width filter for images (default: 100)
        min_height: Minimum height filter for images (default: 100)
        max_images: Maximum number of images to return (default: None for all)

    Returns:
        List of dictionaries containing image information sorted by size
    """
    try:
        page = await browser_session.get_current_page()
        raw_html = await page.content()
        return await extract_webpage_images_sorted(raw_html, min_width, min_height, max_images)
    except Exception as e:
        print(f"Error extracting images from browser session: {str(e)}")
        return []
