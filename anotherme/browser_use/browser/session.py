from __future__ import annotations

import asyncio
import base64
import json
import logging
import os
import re
import time
from dataclasses import dataclass
from functools import wraps
from pathlib import Path
from typing import Any, Self
from urllib.parse import urlparse

import psutil
from patchright.async_api import <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>
from playwright.async_api import <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>
from playwright.async_api import <PERSON><PERSON>erContext as <PERSON>wrightBrowserContext
from playwright.async_api import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    FrameLoc<PERSON>,
    <PERSON>,
    Playwright,
    async_playwright,
)
from pydantic import (
    AliasChoices,
    BaseModel,
    ConfigDict,
    Field,
    InstanceOf,
    PrivateAttr,
    model_validator,
)

from anotherme.browser_use.browser.profile import BrowserProfile
from anotherme.browser_use.browser.views import (
    <PERSON>rowserError,
    BrowserStateSummary,
    TabInfo,
    URLNotAllowedError,
)
from anotherme.browser_use.dom.clickable_element_processor.service import (
    ClickableElementProcessor,
)
from anotherme.browser_use.dom.service import DomService
from anotherme.browser_use.dom.views import D<PERSON><PERSON><PERSON>N<PERSON>, SelectorMap
from anotherme.browser_use.utils import (
    match_url_with_domain_pattern,
    time_execution_async,
    time_execution_sync,
)

# Check if running in Docker
IN_DOCKER = os.environ.get("IN_DOCKER", "false").lower()[0] in "ty1"

logger = logging.getLogger("browser_use.browser.session")


_GLOB_WARNING_SHOWN = False  # used inside _is_url_allowed to avoid spamming the logs with the same warning multiple times


def _log_glob_warning(domain: str, glob: str):
    global _GLOB_WARNING_SHOWN
    if not _GLOB_WARNING_SHOWN:
        logger.warning(
            # glob patterns are very easy to mess up and match too many domains by accident
            # e.g. if you only need to access gmail, don't use *.google.com because an attacker could convince the agent to visit a malicious doc
            # on docs.google.com/s/some/evil/doc to set up a prompt injection attack
            f"⚠️ Allowing agent to visit {domain} based on allowed_domains=['{glob}', ...]. Set allowed_domains=['{domain}', ...] explicitly to avoid matching too many domains!"
        )
        _GLOB_WARNING_SHOWN = True


def _log_pretty_url(s: str, max_len: int | None = 22) -> str:
    """Truncate/pretty-print a URL with a maximum length, removing the protocol and www. prefix"""
    s = s.replace("https://", "").replace("http://", "").replace("www.", "")
    if max_len is not None and len(s) > max_len:
        return s[:max_len] + "…"
    return s


def _log_pretty_path(path: Path) -> str:
    """Pretty-print a path, shorten home dir to ~ and cwd to ."""
    return (
        str(path or "")
        .replace(str(Path.home()), "~")
        .replace(str(Path.cwd().resolve()), ".")
    )


def require_initialization(func):
    """decorator for BrowserSession methods to require the BrowserSession be already active"""

    @wraps(func)
    def wrapper(self, *args, **kwargs):
        if not self.initialized:
            raise RuntimeError(
                "BrowserSession(...).start() must be called first to launch or connect to the browser"
            )
        if not self.agent_current_page or self.agent_current_page.is_closed():
            self.agent_current_page = (
                self.browser_context.pages[0] if self.browser_context.pages else None
            )

        if not self.agent_current_page or self.agent_current_page.is_closed():
            self.create_new_tab()
            assert self.agent_current_page and not self.agent_current_page.is_closed()

        if not hasattr(self, "_cached_browser_state_summary"):
            raise RuntimeError(
                "BrowserSession(...).start() must be called first to initialize the browser session"
            )

        return func(self, *args, **kwargs)

    return wrapper


DEFAULT_BROWSER_PROFILE = BrowserProfile()


@dataclass
class CachedClickableElementHashes:
    """
    Clickable elements hashes for the last state
    """

    url: str
    hashes: set[str]


class BrowserSession(BaseModel):
    """
    Represents an active browser session with a running browser process somewhere.

    Chromium flags should be passed via extra_launch_args.
    Extra Playwright launch options (e.g., handle_sigterm, handle_sigint) can be passed as kwargs to BrowserSession and will be forwarded to the launch() call.
    """

    model_config = ConfigDict(
        extra="allow",
        validate_assignment=False,
        revalidate_instances="always",
        frozen=False,
        arbitrary_types_allowed=True,
        populate_by_name=True,
    )
    # this class accepts arbitrary extra **kwargs in init because of the extra='allow' pydantic option
    # they are saved on the model, then applied to self.browser_profile via .apply_session_overrides_to_profile()

    # template profile for the BrowserSession, will be copied at init/validation time, and overrides applied to the copy
    browser_profile: InstanceOf[BrowserProfile] = Field(
        default=DEFAULT_BROWSER_PROFILE,
        description="BrowserProfile() instance containing config for the BrowserSession",
        validation_alias=AliasChoices(
            "profile", "config", "new_context_config"
        ),  # abbreviations = 'profile', old deprecated names = 'config', 'new_context_config'
    )

    # runtime props/state: these can be passed in as props at init, or get auto-setup by BrowserSession.start()
    wss_url: str | None = Field(
        default=None,
        description="WSS URL of the node.js playwright browser server to connect to, outputted by (await chromium.launchServer()).wsEndpoint()",
    )
    cdp_url: str | None = Field(
        default=None,
        description="CDP URL of the browser to connect to, e.g. http://localhost:9222 or ws://127.0.0.1:9222/devtools/browser/387adf4c-243f-4051-a181-46798f4a46f4",
    )
    browser_pid: int | None = Field(
        default=None,
        description="pid of a running chromium-based browser process to connect to on localhost",
        validation_alias=AliasChoices("chrome_pid"),  # old deprecated name = chrome_pid
    )
    playwright: Playwright | PatchrightPlaywright | Playwright | None = Field(
        default=None,
        description="Playwright library object returned by: await (playwright or patchright).async_playwright().start()",
        exclude=True,
    )
    browser: InstanceOf[PlaywrightBrowser] | None = Field(
        default=None,
        description="playwright Browser object to use (optional)",
        validation_alias=AliasChoices("playwright_browser"),
        exclude=True,
    )
    browser_context: InstanceOf[PlaywrightBrowserContext] | None = Field(
        default=None,
        description="playwright BrowserContext object to use (optional)",
        validation_alias=AliasChoices("playwright_browser_context", "context"),
        exclude=True,
    )

    # runtime state: state that changes during the lifecycle of a BrowserSession(), updated by the methods below
    initialized: bool = Field(
        default=False,
        description="Mark BrowserSession launch/connection as already ready and skip setup (not recommended)",
        validation_alias=AliasChoices("is_initialized"),
    )
    agent_current_page: InstanceOf[Page] | None = (
        Field(  # mutated by self.create_new_tab(url)
            default=None,
            description="Foreground Page that the agent is focused on",
            validation_alias=AliasChoices(
                "current_page", "page"
            ),  # alias page= allows passing in a playwright Page object easily
            exclude=True,
        )
    )
    human_current_page: InstanceOf[Page] | None = (
        Field(  # mutated by self._setup_current_page_change_listeners()
            default=None,
            description="Foreground Page that the human is focused on",
            exclude=True,
        )
    )

    _cached_browser_state_summary: BrowserStateSummary | None = PrivateAttr(
        default=None
    )
    _cached_clickable_element_hashes: CachedClickableElementHashes | None = PrivateAttr(
        default=None
    )

    @model_validator(mode="after")
    def apply_session_overrides_to_profile(self) -> Self:
        """Apply any extra **kwargs passed to BrowserSession(...) as config overrides on top of browser_profile"""
        session_own_fields = type(self).model_fields.keys()

        # get all the extra kwarg overrides passed to BrowserSession(...) that are actually
        # config Fields tracked by BrowserProfile, instead of BrowserSession's own args
        profile_overrides = self.model_dump(exclude=set(session_own_fields))

        # FOR REPL DEBUGGING ONLY, NEVER ALLOW CIRCULAR REFERENCES IN REAL CODE:
        # self.browser_profile._in_use_by_session = self

        # replace browser_profile with patched version
        self.browser_profile = self.browser_profile.model_copy(update=profile_overrides)

        # FOR REPL DEBUGGING ONLY, NEVER ALLOW CIRCULAR REFERENCES IN REAL CODE:
        # self.browser_profile._in_use_by_session = self

        return self

    # def __getattr__(self, key: str) -> Any:
    # 	"""
    # 	fall back to getting any attrs from the underlying self.browser_profile when not defined on self.
    # 	(extra kwargs passed e.g. BrowserSession(extra_kwarg=124) on init get saved into self.browser_profile on validation,
    # 	so this also allows you to read those: browser_session.extra_kwarg => browser_session.browser_profile.extra_kwarg)
    # 	"""
    # 	return getattr(self.browser_profile, key)

    async def start(self) -> Self:
        """
        Starts the browser session by either connecting to an existing browser or launching a new one.
        Precedence order for launching/connecting:
                1. page=Page playwright object, will use its page.context as browser_context
                2. browser_context=PlaywrightBrowserContext object, will use its browser
                3. browser=PlaywrightBrowser object, will use its first available context
                4. browser_pid=int, will connect to a local chromium-based browser via pid
                5. wss_url=str, will connect to a remote playwright browser server via WSS
                6. cdp_url=str, will connect to a remote chromium-based browser via CDP
                7. playwright=Playwright object, will use its chromium instance to launch a new browser
        """

        # apply last-minute runtime-computed options to the the browser_profile, validate profile, set up folders on disk
        assert isinstance(self.browser_profile, BrowserProfile)
        self.browser_profile.prepare_user_data_dir()  # create/unlock the <user_data_dir>/SingletonLock
        self.browser_profile.detect_display_configuration()  # adjusts config values, must come before launch/connect

        # launch/connect to the browser:
        # setup playwright library client, Browser, and BrowserContext objects
        await self.setup_playwright()
        await self.setup_browser_via_passed_objects()
        await self.setup_browser_via_browser_pid()
        await self.setup_browser_via_wss_url()
        await self.setup_browser_via_cdp_url()
        await self.setup_new_browser_context()  # creates a new context in existing browser or launches a new persistent context
        assert self.browser_context, f"Failed to connect to or create a new BrowserContext for browser={self.browser}"

        # resize the existing pages and set up foreground tab detection
        await self._setup_viewports()
        await self._setup_current_page_change_listeners()

        self.initialized = True

        return self

    async def stop(self) -> None:
        """Shuts down the BrowserSession, killing the browser process if keep_alive=False"""

        self.initialized = False

        if self.browser_profile.keep_alive:
            return  # nothing to do if keep_alive=True, leave the browser running

        if self.browser_context or self.browser:
            try:
                await (self.browser_context or self.browser).close()
                logger.info(
                    f"🛑 Stopped the Browser "
                    f"(keep_alive=False user_data_dir={_log_pretty_path(self.browser_profile.user_data_dir) or '<incognito>'})"
                )
                self.browser_context = None
            except Exception as e:
                logger.debug(
                    f"❌ Error closing playwright BrowserContext {self.browser_context}: {type(e).__name__}: {e}"
                )

        # kill the chrome subprocess if we were the ones that started it
        if self.browser_pid:
            try:
                psutil.Process(pid=self.browser_pid).terminate()
                logger.info(
                    f"⏹ Shut down the browser subprocess with browser_pid={self.browser_pid} (keep_alive=False)"
                )
                self.browser_pid = None
            except Exception as e:
                if "NoSuchProcess" not in type(e).__name__:
                    logger.debug(
                        f"❌ Error terminating chrome subprocess pid={self.browser_pid}: {type(e).__name__}: {e}"
                    )

    async def close(self) -> None:
        """Deprecated: Provides backwards-compatibility with old class method Browser().close()"""
        await self.stop()

    async def new_context(self, **kwargs):
        """Deprecated: Provides backwards-compatibility with old class method Browser().new_context()"""
        return self

    async def __aenter__(self) -> BrowserSession:
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()

    async def setup_playwright(self) -> None:
        """
        Set up playwright library client object: usually the result of (await async_playwright().start())
        Override to customize the set up of the playwright or patchright library object
        """
        self.playwright = self.playwright or (await async_playwright().start())

        # if isinstance(self.playwright, PatchrightPlaywright):
        # 	# patchright handles all its own default args, dont mess with them
        # 	self.browser_profile.ignore_default_args = True

        # return self.playwright

    async def setup_browser_via_passed_objects(self) -> None:
        """Override to customize the set up of the connection to an existing browser"""

        # 1. check for a passed Page object, if present, it always takes priority, set browser_context = page.context
        self.browser_context = (
            (self.agent_current_page and self.agent_current_page.context)
            or self.browser_context
            or None
        )

        # 2. if we have a context now, it always takes precedence, set browser = context.browser, otherwise use the passed browser
        self.browser = (
            (self.browser_context and self.browser_context.browser)
            or self.browser
            or None
        )

        if self.browser or self.browser_context:
            logger.info(
                f"🌎 Connected to existing user-provided browser_context: {self.browser_context}"
            )
            self._set_browser_keep_alive(
                True
            )  # we connected to an existing browser, dont kill it at the end

    async def setup_browser_via_browser_pid(self) -> None:
        """if browser_pid is provided, calcuclate its CDP URL by looking for --remote-debugging-port=... in its CLI args, then connect to it"""

        if self.browser or self.browser_context:
            return  # already connected to a browser
        if not self.browser_pid:
            return  # no browser_pid provided, nothing to do

        chrome_process = psutil.Process(pid=self.browser_pid)
        assert chrome_process.is_running(), "Chrome process is not running"
        args = chrome_process.cmdline()
        debug_port = (
            next(
                (arg for arg in args if arg.startswith("--remote-debugging-port=")), ""
            )
            .split("=")[-1]
            .strip()
        )
        assert debug_port, f"Could not find --remote-debugging-port=... to connect to in browser launch args: browser_pid={self.browser_pid} {args}"
        # we could automatically relaunch the browser process with that arg added here, but they may have tabs open they dont want to lose
        self.cdp_url = self.cdp_url or f"http://localhost:{debug_port}/"
        logger.info(
            f"🌎 Connecting to existing local browser process: browser_pid={self.browser_pid} on {self.cdp_url}"
        )
        self.browser = self.browser or await self.playwright.chromium.connect_over_cdp(
            self.cdp_url,
            **self.browser_profile.kwargs_for_connect().model_dump(),
        )
        self._set_browser_keep_alive(
            True
        )  # we connected to an existing browser, dont kill it at the end

    async def setup_browser_via_wss_url(self) -> None:
        """check for a passed wss_url, connect to a remote playwright browser server via WSS"""

        if self.browser or self.browser_context:
            return  # already connected to a browser
        if not self.wss_url:
            return  # no wss_url provided, nothing to do

        logger.info(
            f"🌎 Connecting to existing remote chromium playwright node.js server over WSS: {self.wss_url}"
        )
        self.browser = self.browser or await self.playwright.chromium.connect(
            self.wss_url,
            **self.browser_profile.kwargs_for_connect().model_dump(),
        )
        self._set_browser_keep_alive(
            True
        )  # we connected to an existing browser, dont kill it at the end

    async def setup_browser_via_cdp_url(self) -> None:
        """check for a passed cdp_url, connect to a remote chromium-based browser via CDP"""

        if self.browser or self.browser_context:
            return  # already connected to a browser
        if not self.cdp_url:
            return  # no cdp_url provided, nothing to do

        logger.info(
            f"🌎 Connecting to existing remote chromium-based browser over CDP: {self.cdp_url}"
        )
        self.browser = self.browser or await self.playwright.chromium.connect_over_cdp(
            self.cdp_url,
            **self.browser_profile.kwargs_for_connect().model_dump(),
        )
        self._set_browser_keep_alive(
            True
        )  # we connected to an existing browser, dont kill it at the end

    async def setup_new_browser_context(self) -> None:
        """Launch a new browser and browser_context"""
        current_process = psutil.Process(os.getpid())
        child_pids_before_launch = {
            child.pid for child in current_process.children(recursive=True)
        }

        # if we have a browser object but no browser_context, use the first context discovered or make a new one
        if self.browser and not self.browser_context:
            if self.browser.contexts:
                self.browser_context = self.browser.contexts[0]
                logger.info(
                    f"🌎 Using first browser_context available in existing browser: {self.browser_context}"
                )
            else:
                self.browser_context = await self.browser.new_context(
                    **self.browser_profile.kwargs_for_new_context().model_dump()
                )
                # storage_info = (
                #     f" + loaded storage_state from {self.browser_profile.storage_state if self.browser_profile.storage_state else 0}"
                #     if self.browser_profile.storage_state
                #     else ""
                # )
                logger.info(
                    f"🌎 Created new empty browser_context from {self.browser_profile.storage_state}"
                )

        # if we still have no browser_context by now, launch a new local one using launch_persistent_context()
        if not self.browser_context:
            logger.info(
                f"🌎 Launching local browser "
                f"driver={str(type(self.playwright).__module__).split('.')[0]} channel={self.browser_profile.channel.name.lower()} "
                f"user_data_dir={_log_pretty_path(self.browser_profile.user_data_dir) if self.browser_profile.user_data_dir else '<incognito>'}"
            )
            if not self.browser_profile.user_data_dir:
                # if no user_data_dir is provided, launch an incognito context with no persistent user_data_dir
                self.browser = self.browser or await self.playwright.chromium.launch(
                    **self.browser_profile.kwargs_for_launch().model_dump()
                )
                self.browser_context = await self.browser.new_context(
                    **self.browser_profile.kwargs_for_new_context().model_dump()
                )
            else:
                # user data dir was provided, prepare it for use
                self.browser_profile.prepare_user_data_dir()

                # search for potentially conflicting local processes running on the same user_data_dir
                for proc in psutil.process_iter(["pid", "cmdline"]):
                    if f"--user-data-dir={self.browser_profile.user_data_dir}" in (
                        proc.info["cmdline"] or []
                    ):
                        logger.warning(
                            f"🚨 Found potentially conflicting browser process browser_pid={proc.info['pid']} "
                            f"already running with the same user_data_dir={_log_pretty_path(self.browser_profile.user_data_dir)}"
                        )
                        # self._fork_locked_user_data_dir()
                        break

                # if a user_data_dir is provided, launch a persistent context with that user_data_dir
                self.browser_context = await self.playwright.chromium.launch_persistent_context(
                    **self.browser_profile.kwargs_for_launch_persistent_context().model_dump()
                )

        self.browser = (
            self.browser_context and self.browser_context.browser
        ) or self.browser
        # ^ this can unfortunately still be None at the end ^
        # playwright does not give us a browser object at all when we use launch_persistent_context()!

        # Detect any new child chrome processes that we might have launched above
        child_pids_after_launch = {
            child.pid for child in current_process.children(recursive=True)
        }
        new_child_pids = child_pids_after_launch - child_pids_before_launch
        new_child_procs = [psutil.Process(pid) for pid in new_child_pids]
        new_chrome_procs = [
            proc
            for proc in new_child_procs
            if "Helper" not in proc.name() and proc.status() == "running"
        ]
        if new_chrome_procs and not self.browser_pid:
            self.browser_pid = new_chrome_procs[0].pid
            logger.debug(
                f" ↳ Spawned browser subprocess: browser_pid={self.browser_pid} {' '.join(new_chrome_procs[0].cmdline())}"
            )
            self._set_browser_keep_alive(
                False
            )  # close the browser at the end because we launched it

        if self.browser:
            connection_method = (
                "WSS"
                if self.wss_url
                else "CDP"
                if (self.cdp_url and not self.browser_pid)
                else "Local"
            )
            assert self.browser.is_connected(), f"Browser is not connected, did the browser process crash or get killed? (connection method: {connection_method})"
            logger.debug(
                f"🌎 {connection_method} browser connected: v{self.browser.version} {self.cdp_url or self.wss_url or self.browser_profile.executable_path or '(playwright)'}"
            )

        assert self.browser_context, f"Failed to create a playwright BrowserContext {self.browser_context} for browser={self.browser}"

    # async def _fork_locked_user_data_dir(self) -> None:
    # 	"""Fork an in-use user_data_dir by cloning it to a new location to allow a second browser to use it"""
    # 	# TODO: implement copy-on-write using overlayfs or zfs or something
    # 	suffix_num = str(self.browser_profile.user_data_dir).rsplit('.', 1)[-1] or '1'
    # 	suffix_num = int(suffix_num) if suffix_num.isdigit() else 1
    # 	dir_name = self.browser_profile.user_data_dir.name
    # 	incremented_name = dir_name.replace(f'.{suffix_num}', f'.{suffix_num + 1}')
    # 	fork_path = self.browser_profile.user_data_dir.parent / incremented_name

    # 	# keep incrementing the suffix_num until we find a path that doesn't exist
    # 	while fork_path.exists():
    # 		suffix_num += 1
    # 		fork_path = self.browser_profile.user_data_dir.parent / (dir_name.rsplit('.', 1)[0] + f'.{suffix_num}')

    # 	# use shutil to recursively copy the user_data_dir to a new location
    # 	shutil.copytree(
    # 		str(self.browser_profile.user_data_dir),
    # 		str(fork_path),
    # 		symlinks=True,
    # 		ignore_dangling_symlinks=True,
    # 		dirs_exist_ok=False,
    # 	)
    # 	self.browser_profile.user_data_dir = fork_path
    # 	self.browser_profile.prepare_user_data_dir()

    async def _setup_current_page_change_listeners(self) -> None:
        # Uses a combination of:
        # - visibilitychange events
        # - window focus/blur events
        # - pointermove events

        # This annoying multi-method approach is needed for more reliable detection across browsers because playwright provides no API for this.

        # TODO: pester the playwright team to add a new event that fires when a headful tab is focused.
        # OR implement a browser-use chrome extension that acts as a bridge to the chrome.tabs API.

        #         - https://github.com/microsoft/playwright/issues/1290
        #         - https://github.com/microsoft/playwright/issues/2286
        #         - https://github.com/microsoft/playwright/issues/3570
        #         - https://github.com/microsoft/playwright/issues/13989

        # set up / detect foreground page
        assert self.browser_context is not None, "BrowserContext object is not set"
        pages = self.browser_context.pages
        foreground_page = None
        if pages:
            foreground_page = pages[0]
            logger.debug(
                f"📜 Found {len(pages)} existing tabs in browser, agent will start focused on Tab [{pages.index(foreground_page)}]: {foreground_page.url}"
            )
        else:
            foreground_page = await self.browser_context.new_page()
            pages = [foreground_page]
            logger.debug("➕ Opened new tab in empty browser context...")

        self.agent_current_page = self.agent_current_page or foreground_page
        self.human_current_page = self.human_current_page or foreground_page

        def _BrowserUseonTabVisibilityChange(source: dict[str, str]):
            """hook callback fired when init script injected into a page detects a focus event"""
            new_page = source["page"]

            # Update human foreground tab state
            old_foreground = self.human_current_page
            assert self.browser_context is not None, "BrowserContext object is not set"
            assert old_foreground is not None, "Old foreground page is not set"
            old_tab_idx = self.browser_context.pages.index(old_foreground)
            self.human_current_page = new_page
            new_tab_idx = self.browser_context.pages.index(new_page)

            # Log before and after for debugging
            old_url = old_foreground and old_foreground.url or "about:blank"
            new_url = new_page and new_page.url or "about:blank"
            agent_url = (
                self.agent_current_page and self.agent_current_page.url or "about:blank"
            )
            agent_tab_idx = self.browser_context.pages.index(self.agent_current_page)
            if old_url != new_url:
                logger.info(
                    f"👁️ Foregound tab changed by human from [{old_tab_idx}]{_log_pretty_url(old_url)} "
                    f"➡️ [{new_tab_idx}]{_log_pretty_url(new_url)} "
                    f"(agent will stay on [{agent_tab_idx}]{_log_pretty_url(agent_url)})"
                )

        try:
            await self.browser_context.expose_binding(
                "_BrowserUseonTabVisibilityChange", _BrowserUseonTabVisibilityChange
            )

        except Exception as e:
            if (
                'Function "_BrowserUseonTabVisibilityChange" has been already registered'
                in str(e)
            ):
                logger.debug(
                    '⚠️ Function "_BrowserUseonTabVisibilityChange" has been already registered, '
                    "this is likely because the browser was already started with an existing BrowserSession()"
                )

            else:
                raise

        update_tab_focus_script = """
			// --- Method 1: visibilitychange event (unfortunately *all* tabs are always marked visible by playwright, usually does not fire) ---
			document.addEventListener('visibilitychange', async () => {
				if (document.visibilityState === 'visible') {
					await window._BrowserUseonTabVisibilityChange({ source: 'visibilitychange', url: document.location.href });
					console.log('BrowserUse Foreground tab change event fired', document.location.href);
				}
			});

			// --- Method 2: focus/blur events, most reliable method for headful browsers ---
			window.addEventListener('focus', async () => {
				await window._BrowserUseonTabVisibilityChange({ source: 'focus', url: document.location.href });
				console.log('BrowserUse Foreground tab change event fired', document.location.href);
			});

			// --- Method 3: pointermove events (may be fired by agent if we implement AI hover movements, also very noisy) ---
			// Use a throttled handler to avoid excessive calls
			// let lastMove = 0;
			// window.addEventListener('pointermove', async () => {
			// 	const now = Date.now();
			// 	if (now - lastMove > 1000) {  // Throttle to once per second
			// 		lastMove = now;
			// 		await window._BrowserUseonTabVisibilityChange({ source: 'pointermove', url: document.location.href });
			//      console.log('BrowserUse Foreground tab change event fired', document.location.href);
			// 	}
			// });
		"""
        await self.browser_context.add_init_script(update_tab_focus_script)

        # Set up visibility listeners for all existing tabs
        for page in self.browser_context.pages:
            try:
                # logger.debug(f'👁️ Added visibility listener to existing tab: {page.url}')
                await page.evaluate(update_tab_focus_script)
            except Exception as e:
                page_idx = self.browser_context.pages.index(page)
                logger.debug(
                    f"⚠️ Failed to add visibility listener to existing tab, is it crashed or ignoring CDP commands?: [{page_idx}]{page.url}: {type(e).__name__}: {e}"
                )

    async def _setup_viewports(self) -> None:
        """Resize any existing page viewports to match the configured size"""

        # log the viewport settings to terminal
        viewport = self.browser_profile.viewport
        logger.debug(
            "📐 Setting up viewport: "
            + f"headless={self.browser_profile.headless} "
            + (
                f"window={self.browser_profile.window_size['width']}x{self.browser_profile.window_size['height']}px "
                if self.browser_profile.window_size
                else "(no window) "
            )
            + (
                f"screen={self.browser_profile.screen['width']}x{self.browser_profile.screen['height']}px "
                if self.browser_profile.screen
                else ""
            )
            + (
                f"viewport={viewport['width']}x{viewport['height']}px "
                if viewport
                else "(no viewport) "
            )
            + f"device_scale_factor={self.browser_profile.device_scale_factor or 1.0} "
            + f"is_mobile={self.browser_profile.is_mobile} "
            + (
                f"color_scheme={self.browser_profile.color_scheme.value} "
                if self.browser_profile.color_scheme
                else ""
            )
            + (
                f"locale={self.browser_profile.locale} "
                if self.browser_profile.locale
                else ""
            )
            + (
                f"timezone_id={self.browser_profile.timezone_id} "
                if self.browser_profile.timezone_id
                else ""
            )
            + (
                f"geolocation={self.browser_profile.geolocation} "
                if self.browser_profile.geolocation
                else ""
            )
            + (
                f"permissions={','.join(self.browser_profile.permissions or ['<none>'])} "
            )
        )

        # if we have any viewport settings in the profile, make sure to apply them to the entire browser_context as defaults
        if self.browser_profile.permissions:
            try:
                await self.browser_context.grant_permissions(
                    self.browser_profile.permissions
                )
            except Exception as e:
                logger.warning(
                    f"⚠️ Failed to grant browser permissions {self.browser_profile.permissions}: {type(e).__name__}: {e}"
                )
        try:
            if self.browser_profile.default_timeout:
                await self.browser_context.set_default_timeout(
                    self.browser_profile.default_timeout
                )
            if self.browser_profile.default_navigation_timeout:
                await self.browser_context.set_default_navigation_timeout(
                    self.browser_profile.default_navigation_timeout
                )
        except Exception as e:
            logger.warning(
                f"⚠️ Failed to set playwright timeout settings "
                f"cdp_api={self.browser_profile.default_timeout} "
                f"navigation={self.browser_profile.default_navigation_timeout}: {type(e).__name__}: {e}"
            )
        try:
            if self.browser_profile.extra_http_headers:
                await self.browser_context.set_extra_http_headers(
                    self.browser_profile.extra_http_headers
                )
        except Exception as e:
            logger.warning(
                f"⚠️ Failed to setup playwright extra_http_headers: {type(e).__name__}: {e}"
            )  # dont print the secret header contents in the logs!

        try:
            if self.browser_profile.geolocation:
                await self.browser_context.set_geolocation(
                    self.browser_profile.geolocation
                )
        except Exception as e:
            logger.warning(
                f"⚠️ Failed to update browser geolocation {self.browser_profile.geolocation}: {type(e).__name__}: {e}"
            )

        # if self.storage_state:
        #   TODO: implement applying self.stroage_state to an existing browser_context, currently only works on browser.new_context() I think
        # 	await self.browser_context.set_storage_state(self.storage_state)

        page = None

        for page in self.browser_context.pages:
            # apply viewport size settings to any existing pages
            if viewport:
                await page.set_viewport_size(viewport)

            # show browser-use dvd screensaver-style bouncing loading animation on any about:blank pages
            if page.url == "about:blank":
                await self._show_dvd_screensaver_loading_animation(page)

        page = page or (await self.browser_context.new_page())

        if (
            (not viewport)
            and (self.browser_profile.window_size is not None)
            and not self.browser_profile.headless
        ):
            # attempt to resize the actual browser window

            # cdp api: https://chromedevtools.github.io/devtools-protocol/tot/Browser/#method-setWindowBounds
            try:
                cdp_session = await page.context.new_cdp_session(page)
                window_id_result = await cdp_session.send("Browser.getWindowForTarget")
                await cdp_session.send(
                    "Browser.setWindowBounds",
                    {
                        "windowId": window_id_result["windowId"],
                        "bounds": {
                            **self.browser_profile.window_size,
                            "windowState": "normal",  # Ensure window is not minimized/maximized
                        },
                    },
                )
                await cdp_session.detach()
            except Exception as e:

                def _log_size(size):
                    return f"{size['width']}x{size['height']}px"

                try:
                    # fallback to javascript resize if cdp setWindowBounds fails
                    await page.evaluate(
                        """(width, height) => {window.resizeTo(width, height)}""",
                        **self.browser_profile.window_size,
                    )
                    return
                except Exception:
                    pass

                logger.warning(
                    f"⚠️ Failed to resize browser window to {_log_size(self.browser_profile.window_size)} using CDP setWindowBounds: {type(e).__name__}: {e}"
                )

    def _set_browser_keep_alive(self, keep_alive: bool | None) -> None:
        """set the keep_alive flag on the browser_profile, defaulting to True if keep_alive is None"""
        if self.browser_profile.keep_alive is None:
            self.browser_profile.keep_alive = keep_alive

    # --- Tab management ---
    async def get_current_page(self) -> Page:
        """Get the current page + ensure it's not None / closed"""

        if not self.initialized:
            await self.start()

        # get-or-create the browser_context if it's not already set up
        if not self.browser_context:
            await self.start()
            assert self.browser_context, "BrowserContext is not set up"

        # if either focused page is closed, clear it so we dont use a dead object
        if (not self.human_current_page) or self.human_current_page.is_closed():
            self.human_current_page = None
        if (not self.agent_current_page) or self.agent_current_page.is_closed():
            self.agent_current_page = None

        # if either one is None, fallback to using the other one for both
        self.agent_current_page = (
            self.agent_current_page or self.human_current_page or None
        )
        self.human_current_page = (
            self.human_current_page or self.agent_current_page or None
        )

        # if both are still None, fallback to using the first open tab we can find
        if self.agent_current_page is None:
            if self.browser_context.pages:
                first_available_tab = self.browser_context.pages[0]
                self.agent_current_page = first_available_tab
                self.human_current_page = first_available_tab
            else:
                # if all tabs are closed, open a new one
                new_tab = await self.create_new_tab()
                self.agent_current_page = new_tab
                self.human_current_page = new_tab

        assert (
            self.agent_current_page is not None
        ), "Failed to find or create a new page for the agent"
        assert (
            self.human_current_page is not None
        ), "Failed to find or create a new page for the human"

        return self.agent_current_page

    @property
    def tabs(self) -> list[Page]:
        if not self.browser_context:
            return []
        return list(self.browser_context.pages)

    @require_initialization
    async def new_tab(self, url: str | None = None) -> Page:
        return await self.create_new_tab(url=url)

    @require_initialization
    async def switch_tab(self, tab_index: int) -> Page:
        pages = self.browser_context.pages
        if not pages or tab_index >= len(pages):
            raise IndexError("Tab index out of range")
        page = pages[tab_index]
        self.agent_current_page = page

        return page

    @require_initialization
    async def wait_for_element(self, selector: str, timeout: int = 10000) -> None:
        page = await self.get_current_page()
        await page.wait_for_selector(selector, state="visible", timeout=timeout)

    @require_initialization
    @time_execution_async("--remove_highlights")
    async def remove_highlights(self):
        """
        Removes all highlight overlays and labels created by the highlightElement function.
        Handles cases where the page might be closed or inaccessible.
        """
        page = await self.get_current_page()
        try:
            await page.evaluate(
                """
                try {
                    // Remove the highlight container and all its contents
                    const container = document.getElementById('playwright-highlight-container');
                    if (container) {
                        container.remove();
                    }

                    // Remove highlight attributes from elements
                    const highlightedElements = document.querySelectorAll('[browser-user-highlight-id^="playwright-highlight-"]');
                    highlightedElements.forEach(el => {
                        el.removeAttribute('browser-user-highlight-id');
                    });
                } catch (e) {
                    console.error('Failed to remove highlights:', e);
                }
                """
            )
        except Exception as e:
            logger.debug(
                f"⚠  Failed to remove highlights (this is usually ok): {type(e).__name__}: {e}"
            )
            # Don't raise the error since this is not critical functionality

    @require_initialization
    async def get_dom_element_by_index(self, index: int) -> Any | None:
        """Get DOM element by index."""
        selector_map = await self.get_selector_map()
        return selector_map.get(index)

    @time_execution_async("--click_element_node")
    async def _click_element_node(self, element_node: DOMElementNode) -> str | None:
        """
        Optimized method to click an element using xpath.
        """
        page = await self.get_current_page()
        try:
            # Highlight before clicking
            # if element_node.highlight_index is not None:
            # 	await self._update_state(focus_element=element_node.highlight_index)

            element_handle = await self.get_locate_element(element_node)

            if element_handle is None:
                raise Exception(f"Element: {repr(element_node)} not found")

            async def perform_click(click_func):
                """Performs the actual click, handling both download
                and navigation scenarios."""
                if self.browser_profile.save_downloads_path:
                    try:
                        # Try short-timeout expect_download to detect a file download has been been triggered
                        async with page.expect_download(timeout=5000) as download_info:
                            await click_func()
                        download = await download_info.value
                        # Determine file path
                        suggested_filename = download.suggested_filename
                        unique_filename = await self._get_unique_filename(
                            self.browser_profile.save_downloads_path, suggested_filename
                        )
                        download_path = os.path.join(
                            self.browser_profile.save_downloads_path, unique_filename
                        )
                        await download.save_as(download_path)
                        logger.debug(
                            f"⬇️  Download triggered. Saved file to: {download_path}"
                        )
                        return download_path
                    except TimeoutError:
                        # If no download is triggered, treat as normal click
                        logger.debug(
                            "No download triggered within timeout. Checking navigation..."
                        )
                        await page.wait_for_load_state()
                        await self._check_and_handle_navigation(page)
                else:
                    # Standard click logic if no download is expected
                    await click_func()
                    await page.wait_for_load_state()
                    await self._check_and_handle_navigation(page)

            try:
                return await perform_click(lambda: element_handle.click(timeout=1500))
            except URLNotAllowedError as e:
                raise e
            except Exception:
                try:
                    return await perform_click(
                        lambda: page.evaluate("(el) => el.click()", element_handle)
                    )
                except URLNotAllowedError as e:
                    raise e
                except Exception as e:
                    raise Exception(f"Failed to click element: {str(e)}")

        except URLNotAllowedError as e:
            raise e
        except Exception as e:
            raise Exception(
                f"Failed to click element: {repr(element_node)}. Error: {str(e)}"
            )

    @time_execution_async("--get_tabs_info")
    async def get_tabs_info(self) -> list[TabInfo]:
        """Get information about all tabs"""

        tabs_info = []
        for page_id, page in enumerate(self.browser_context.pages):
            try:
                tab_info = TabInfo(
                    page_id=page_id,
                    url=page.url,
                    title=await asyncio.wait_for(page.title(), timeout=1),
                )
            except TimeoutError:
                # page.title() can hang forever on tabs that are crashed/disappeared/about:blank
                # we dont want to try automating those tabs because they will hang the whole script
                logger.debug(
                    "⚠  Failed to get tab info for tab #%s: %s (ignoring)",
                    page_id,
                    page.url,
                )
                tab_info = TabInfo(
                    page_id=page_id,
                    url="about:blank",
                    title="ignore this tab and do not use it",
                )
            tabs_info.append(tab_info)

        return tabs_info

    @require_initialization
    async def close_tab(self, tab_index: int | None = None) -> None:
        pages = self.browser_context.pages
        if not pages:
            return

        if tab_index is None:
            # to tab_index passed, just close the current agent page
            page = await self.get_current_page()
        else:
            # otherwise close the tab at the given index
            page = pages[tab_index]

        await page.close()

        # reset the self.agent_current_page and self.human_current_page references to first available tab
        await self.get_current_page()

    # --- Page navigation ---
    @require_initialization
    async def navigate(self, url: str) -> None:
        if self.agent_current_page:
            await self.agent_current_page.goto(url)
        else:
            await self.create_new_tab(url)

    @require_initialization
    async def refresh(self) -> None:
        if self.agent_current_page and not self.agent_current_page.is_closed():
            await self.agent_current_page.reload()
        else:
            await self.create_new_tab()

    @require_initialization
    async def execute_javascript(self, script: str) -> Any:
        page = await self.get_current_page()
        return await page.evaluate(script)

    async def get_cookies(self) -> list[dict[str, Any]]:
        if self.browser_context:
            return await self.browser_context.cookies()
        return []

    async def save_cookies(self, path: Path | None = None) -> None:
        """
        Save cookies to the specified path or the default cookies_file in the downloads_dir.
        """
        if self.browser_context:
            cookies = await self.browser_context.cookies()
            out_path = path or self.browser_profile.cookies_file
            if out_path:
                # If out_path is not absolute, resolve relative to downloads_dir
                out_path = Path(out_path)
                if not out_path.is_absolute():
                    out_path = Path(self.browser_profile.downloads_dir) / out_path
                out_path.parent.mkdir(parents=True, exist_ok=True)
                out_path.write_text(
                    json.dumps(cookies, indent=4)
                )  # TODO: replace with anyio asyncio or anyio write

    # @property
    # def browser_extension_pages(self) -> list[Page]:
    # 	if not self.browser_context:
    # 		return []
    # 	return [p for p in self.browser_context.pages if p.url.startswith('chrome-extension://')]

    # @property
    # def saved_downloads(self) -> list[Path]:
    # 	"""
    # 	Return a list of files in the downloads_dir.
    # 	"""
    # 	return list(Path(self.browser_profile.downloads_dir).glob('*'))

    async def _wait_for_stable_network(self):
        pending_requests = set()
        last_activity = asyncio.get_event_loop().time()

        page = await self.get_current_page()

        # Define relevant resource types and content types
        RELEVANT_RESOURCE_TYPES = {
            "document",
            "stylesheet",
            "image",
            "font",
            "script",
            "iframe",
        }

        RELEVANT_CONTENT_TYPES = {
            "text/html",
            "text/css",
            "application/javascript",
            "image/",
            "font/",
            "application/json",
        }

        # Additional patterns to filter out
        IGNORED_URL_PATTERNS = {
            # Analytics and tracking
            "analytics",
            "tracking",
            "telemetry",
            "beacon",
            "metrics",
            # Ad-related
            "doubleclick",
            "adsystem",
            "adserver",
            "advertising",
            # Social media widgets
            "facebook.com/plugins",
            "platform.twitter",
            "linkedin.com/embed",
            # Live chat and support
            "livechat",
            "zendesk",
            "intercom",
            "crisp.chat",
            "hotjar",
            # Push notifications
            "push-notifications",
            "onesignal",
            "pushwoosh",
            # Background sync/heartbeat
            "heartbeat",
            "ping",
            "alive",
            # WebRTC and streaming
            "webrtc",
            "rtmp://",
            "wss://",
            # Common CDNs for dynamic content
            "cloudfront.net",
            "fastly.net",
        }

        async def on_request(request):
            # Filter by resource type
            if request.resource_type not in RELEVANT_RESOURCE_TYPES:
                return

            # Filter out streaming, websocket, and other real-time requests
            if request.resource_type in {
                "websocket",
                "media",
                "eventsource",
                "manifest",
                "other",
            }:
                return

            # Filter out by URL patterns
            url = request.url.lower()
            if any(pattern in url for pattern in IGNORED_URL_PATTERNS):
                return

            # Filter out data URLs and blob URLs
            if url.startswith(("data:", "blob:")):
                return

            # Filter out requests with certain headers
            headers = request.headers
            if headers.get("purpose") == "prefetch" or headers.get(
                "sec-fetch-dest"
            ) in [
                "video",
                "audio",
            ]:
                return

            nonlocal last_activity
            pending_requests.add(request)
            last_activity = asyncio.get_event_loop().time()
            # logger.debug(f'Request started: {request.url} ({request.resource_type})')

        async def on_response(response):
            request = response.request
            if request not in pending_requests:
                return

            # Filter by content type if available
            content_type = response.headers.get("content-type", "").lower()

            # Skip if content type indicates streaming or real-time data
            if any(
                t in content_type
                for t in [
                    "streaming",
                    "video",
                    "audio",
                    "webm",
                    "mp4",
                    "event-stream",
                    "websocket",
                    "protobuf",
                ]
            ):
                pending_requests.remove(request)
                return

            # Only process relevant content types
            if not any(ct in content_type for ct in RELEVANT_CONTENT_TYPES):
                pending_requests.remove(request)
                return

            # Skip if response is too large (likely not essential for page load)
            content_length = response.headers.get("content-length")
            if content_length and int(content_length) > 5 * 1024 * 1024:  # 5MB
                pending_requests.remove(request)
                return

            nonlocal last_activity
            pending_requests.remove(request)
            last_activity = asyncio.get_event_loop().time()
            # logger.debug(f'Request resolved: {request.url} ({content_type})')

        # Attach event listeners
        page.on("request", on_request)
        page.on("response", on_response)

        now = asyncio.get_event_loop().time()
        try:
            # Wait for idle time
            start_time = asyncio.get_event_loop().time()
            while True:
                await asyncio.sleep(0.1)
                now = asyncio.get_event_loop().time()
                if (
                    len(pending_requests) == 0
                    and (now - last_activity)
                    >= self.browser_profile.wait_for_network_idle_page_load_time
                ):
                    break
                if now - start_time > self.browser_profile.maximum_wait_page_load_time:
                    logger.debug(
                        f"Network timeout after {self.browser_profile.maximum_wait_page_load_time}s with {len(pending_requests)} "
                        f"pending requests: {[r.url for r in pending_requests]}"
                    )
                    break

        finally:
            # Clean up event listeners
            page.remove_listener("request", on_request)
            page.remove_listener("response", on_response)

        elapsed = now - start_time
        if elapsed > 1:
            logger.debug(
                f"💤 Page network traffic calmed down after {now - start_time:.2f} seconds"
            )

    async def _wait_for_page_and_frames_load(
        self, timeout_overwrite: float | None = None
    ):
        """
        Ensures page is fully loaded before continuing.
        Waits for either network to be idle or minimum WAIT_TIME, whichever is longer.
        Also checks if the loaded URL is allowed.
        """
        # Start timing
        start_time = time.time()

        # Wait for page load
        page = await self.get_current_page()
        try:
            await self._wait_for_stable_network()

            # Check if the loaded URL is allowed
            await self._check_and_handle_navigation(page)
        except URLNotAllowedError as e:
            raise e
        except Exception:
            logger.warning("⚠️  Page load failed, continuing...")
            pass

        # Calculate remaining time to meet minimum WAIT_TIME
        elapsed = time.time() - start_time
        remaining = max(
            (timeout_overwrite or self.browser_profile.minimum_wait_page_load_time)
            - elapsed,
            0,
        )

        # just for logging, calculate how much data was downloaded
        try:
            bytes_used = await page.evaluate("""
				() => {
					let total = 0;
					for (const entry of performance.getEntriesByType('resource')) {
						total += entry.transferSize || 0;
					}
					for (const nav of performance.getEntriesByType('navigation')) {
						total += nav.transferSize || 0;
					}
					return total;
				}
			""")
        except Exception:
            bytes_used = None

        tab_idx = self.tabs.index(page)
        if bytes_used is not None:
            logger.debug(
                f"➡️ Page navigation [{tab_idx}]{_log_pretty_url(page.url, 40)} used {bytes_used / 1024:.1f} KB in {elapsed:.2f}s, waiting +{remaining:.2f}s for all frames to finish"
            )
        else:
            logger.debug(
                f"➡️ Page navigation [{tab_idx}]{_log_pretty_url(page.url, 40)} took {elapsed:.2f}s, waiting +{remaining:.2f}s for all frames to finish"
            )

        # Sleep remaining time if needed
        if remaining > 0:
            await asyncio.sleep(remaining)

    def _is_url_allowed(self, url: str) -> bool:
        """
        Check if a URL is allowed based on the whitelist configuration. SECURITY CRITICAL.

        Supports optional glob patterns and schemes in allowed_domains:
        - *.example.com will match sub.example.com and example.com
        - *google.com will match google.com, agoogle.com, and www.google.com
        - http*://example.com will match http://example.com, https://example.com
        - chrome-extension://* will match chrome-extension://aaaaaaaaaaaa and chrome-extension://bbbbbbbbbbbbb
        """

        if not self.browser_profile.allowed_domains:
            return (
                True  # allowed_domains are not configured, allow everything by default
            )

        # Special case: Always allow 'about:blank' new tab page
        if url == "about:blank":
            return True

        for allowed_domain in self.browser_profile.allowed_domains:
            try:
                if match_url_with_domain_pattern(
                    url, allowed_domain, log_warnings=True
                ):
                    # If it's a pattern with wildcards, show a warning
                    if "*" in allowed_domain:
                        parsed_url = urlparse(url)
                        domain = (
                            parsed_url.hostname.lower() if parsed_url.hostname else ""
                        )
                        _log_glob_warning(domain, allowed_domain)
                    return True
            except AssertionError:
                # This would only happen if about:blank is passed to match_url_with_domain_pattern,
                # which shouldn't occur since we check for it above
                continue

        return False

    async def _check_and_handle_navigation(self, page: Page) -> None:
        """Check if current page URL is allowed and handle if not."""
        if not self._is_url_allowed(page.url):
            logger.warning(f"⛔️  Navigation to non-allowed URL detected: {page.url}")
            try:
                await self.go_back()
            except Exception as e:
                logger.error(
                    f"⛔️  Failed to go back after detecting non-allowed URL: {str(e)}"
                )
            raise URLNotAllowedError(f"Navigation to non-allowed URL: {page.url}")

    async def navigate_to(self, url: str):
        """Navigate the agent's current tab to a URL"""
        if not self._is_url_allowed(url):
            raise BrowserError(f"Navigation to non-allowed URL: {url}")

        page = await self.get_current_page()
        await page.goto(url)
        await page.wait_for_load_state()

    async def refresh_page(self):
        """Refresh the agent's current page"""

        page = await self.get_current_page()
        await page.reload()
        await page.wait_for_load_state()

    async def go_back(self):
        """Navigate the agent's tab back in browser history"""
        try:
            # 10 ms timeout
            page = await self.get_current_page()
            await page.go_back(timeout=10, wait_until="domcontentloaded")

            # await self._wait_for_page_and_frames_load(timeout_overwrite=1.0)
        except Exception as e:
            # Continue even if its not fully loaded, because we wait later for the page to load
            logger.debug(f"⏮️  Error during go_back: {e}")

    async def go_forward(self):
        """Navigate the agent's tab forward in browser history"""
        try:
            page = await self.get_current_page()
            await page.go_forward(timeout=10, wait_until="domcontentloaded")
        except Exception as e:
            # Continue even if its not fully loaded, because we wait later for the page to load
            logger.debug(f"⏭️  Error during go_forward: {e}")

    async def close_current_tab(self):
        """Close the current tab that the agent is working with.

        This closes the tab that the agent is currently using (agent_current_page),
        not necessarily the tab that is visible to the user (human_current_page).
        If they are the same tab, both references will be updated.
        """
        assert self.browser_context is not None, "Browser context is not set"
        assert self.agent_current_page is not None, "Agent current page is not set"

        # Check if this is the foreground tab as well
        is_foreground = self.agent_current_page == self.human_current_page

        # Close the tab
        try:
            await self.agent_current_page.close()
        except Exception as e:
            logger.debug(f"⛔️  Error during close_current_tab: {e}")

        # Clear agent's reference to the closed tab
        self.agent_current_page = None

        # Clear foreground reference if needed
        if is_foreground:
            self.human_current_page = None

        # Switch to the first available tab if any exist
        if self.browser_context.pages:
            await self.switch_to_tab(0)
            # switch_to_tab already updates both tab references

        # Otherwise, the browser will be closed

    async def get_page_html(self) -> str:
        """Get the HTML content of the agent's current page"""
        page = await self.get_current_page()
        return await page.content()

    async def get_page_structure(self) -> str:
        """Get a debug view of the page structure including iframes"""
        debug_script = """(() => {
			function getPageStructure(element = document, depth = 0, maxDepth = 10) {
				if (depth >= maxDepth) return '';

				const indent = '  '.repeat(depth);
				let structure = '';

				// Skip certain elements that clutter the output
				const skipTags = new Set(['script', 'style', 'link', 'meta', 'noscript']);

				// Add current element info if it's not the document
				if (element !== document) {
					const tagName = element.tagName.toLowerCase();

					// Skip uninteresting elements
					if (skipTags.has(tagName)) return '';

					const id = element.id ? `#${element.id}` : '';
					const classes = element.className && typeof element.className === 'string' ?
						`.${element.className.split(' ').filter(c => c).join('.')}` : '';

					// Get additional useful attributes
					const attrs = [];
					if (element.getAttribute('role')) attrs.push(`role="${element.getAttribute('role')}"`);
					if (element.getAttribute('aria-label')) attrs.push(`aria-label="${element.getAttribute('aria-label')}"`);
					if (element.getAttribute('type')) attrs.push(`type="${element.getAttribute('type')}"`);
					if (element.getAttribute('name')) attrs.push(`name="${element.getAttribute('name')}"`);
					if (element.getAttribute('src')) {
						const src = element.getAttribute('src');
						attrs.push(`src="${src.substring(0, 50)}${src.length > 50 ? '...' : ''}"`);
					}

					// Add element info
					structure += `${indent}${tagName}${id}${classes}${attrs.length ? ' [' + attrs.join(', ') + ']' : ''}\\n`;

					// Handle iframes specially
					if (tagName === 'iframe') {
						try {
							const iframeDoc = element.contentDocument || element.contentWindow?.document;
							if (iframeDoc) {
								structure += `${indent}  [IFRAME CONTENT]:\\n`;
								structure += getPageStructure(iframeDoc, depth + 2, maxDepth);
							} else {
								structure += `${indent}  [IFRAME: No access - likely cross-origin]\\n`;
							}
						} catch (e) {
							structure += `${indent}  [IFRAME: Access denied - ${e.message}]\\n`;
						}
					}
				}

				// Get all child elements
				const children = element.children || element.childNodes;
				for (const child of children) {
					if (child.nodeType === 1) { // Element nodes only
						structure += getPageStructure(child, depth + 1, maxDepth);
					}
				}

				return structure;
			}

			return getPageStructure();
		})()"""

        page = await self.get_current_page()
        structure = await page.evaluate(debug_script)
        return structure

    @time_execution_sync(
        "--get_state_summary"
    )  # This decorator might need to be updated to handle async
    async def get_state_summary(
        self, cache_clickable_elements_hashes: bool
    ) -> BrowserStateSummary:
        """Get a summary of the current browser state

        This method builds a BrowserStateSummary object that captures the current state
        of the browser, including url, title, tabs, screenshot, and DOM tree.

        Parameters:
        -----------
        cache_clickable_elements_hashes: bool
                If True, cache the clickable elements hashes for the current state.
                This is used to calculate which elements are new to the LLM since the last message,
                which helps reduce token usage.
        """
        await self._wait_for_page_and_frames_load()
        updated_state = await self._get_updated_state()

        # Find out which elements are new
        # Do this only if url has not changed
        if cache_clickable_elements_hashes:
            # if we are on the same url as the last state, we can use the cached hashes
            if (
                self._cached_clickable_element_hashes
                and self._cached_clickable_element_hashes.url == updated_state.url
            ):
                # Pointers, feel free to edit in place
                updated_state_clickable_elements = (
                    ClickableElementProcessor.get_clickable_elements(
                        updated_state.element_tree
                    )
                )

                for dom_element in updated_state_clickable_elements:
                    dom_element.is_new = (
                        ClickableElementProcessor.hash_dom_element(dom_element)
                        not in self._cached_clickable_element_hashes.hashes  # see which elements are new from the last state where we cached the hashes
                    )
            # in any case, we need to cache the new hashes
            self._cached_clickable_element_hashes = CachedClickableElementHashes(
                url=updated_state.url,
                hashes=ClickableElementProcessor.get_clickable_elements_hashes(
                    updated_state.element_tree
                ),
            )

        assert updated_state
        self._cached_browser_state_summary = updated_state

        # Save cookies if a file is specified
        if self.browser_profile.cookies_file:
            asyncio.create_task(self.save_cookies())

        return self._cached_browser_state_summary

    async def _get_updated_state(self, focus_element: int = -1) -> BrowserStateSummary:
        """Update and return state."""

        page = await self.get_current_page()

        # Check if current page is still valid, if not switch to another available page
        try:
            # Test if page is still accessible
            await page.evaluate("1")
        except Exception as e:
            logger.debug(
                f"👋  Current page is no longer accessible: {type(e).__name__}: {e}"
            )
            raise BrowserError("Browser closed: no valid pages available")

        try:
            await self.remove_highlights()
            dom_service = DomService(page)
            content = await dom_service.get_clickable_elements(
                focus_element=focus_element,
                viewport_expansion=self.browser_profile.viewport_expansion,
                highlight_elements=self.browser_profile.highlight_elements,
            )

            tabs_info = await self.get_tabs_info()

            # Get all cross-origin iframes within the page and open them in new tabs
            # mark the titles of the new tabs so the LLM knows to check them for additional content
            # unfortunately too buggy for now, too many sites use invisible cross-origin iframes for ads, tracking, youtube videos, social media, etc.
            # and it distracts the bot by opening a lot of new tabs
            # iframe_urls = await dom_service.get_cross_origin_iframes()
            # outer_page = self.agent_current_page
            # for url in iframe_urls:
            # 	if url in [tab.url for tab in tabs_info]:
            # 		continue  # skip if the iframe if we already have it open in a tab
            # 	new_page_id = tabs_info[-1].page_id + 1
            # 	logger.debug(f'Opening cross-origin iframe in new tab #{new_page_id}: {url}')
            # 	await self.create_new_tab(url)
            # 	tabs_info.append(
            # 		TabInfo(
            # 			page_id=new_page_id,
            # 			url=url,
            # 			title=f'iFrame opened as new tab, treat as if embedded inside page {outer_page.url}: {page.url}',
            # 			parent_page_url=outer_page.url,
            # 		)
            # 	)

            screenshot_b64 = await self.take_screenshot()
            pixels_above, pixels_below = await self.get_scroll_info(page)

            self.browser_state_summary = BrowserStateSummary(
                element_tree=content.element_tree,
                selector_map=content.selector_map,
                url=page.url,
                title=await page.title(),
                tabs=tabs_info,
                screenshot=screenshot_b64,
                pixels_above=pixels_above,
                pixels_below=pixels_below,
            )

            return self.browser_state_summary
        except Exception as e:
            logger.error(f"❌  Failed to update state: {e}")
            # Return last known good state if available
            if hasattr(self, "browser_state_summary"):
                return self.browser_state_summary
            raise

    # region - Browser Actions
    @time_execution_async("--take_screenshot")
    async def take_screenshot(self, full_page: bool = False) -> str:
        """
        Returns a base64 encoded screenshot of the current page.
        """
        assert self.agent_current_page is not None, "Agent current page is not set"

        # We no longer force tabs to the foreground as it disrupts user focus
        # await self.agent_current_page.bring_to_front()
        page = await self.get_current_page()
        await page.wait_for_load_state()

        screenshot = await self.agent_current_page.screenshot(
            full_page=full_page,
            animations="disabled",
            caret="initial",
        )

        screenshot_b64 = base64.b64encode(screenshot).decode("utf-8")

        # await self.remove_highlights()

        return screenshot_b64

    # endregion

    # region - User Actions

    @staticmethod
    async def _get_unique_filename(directory: str, filename: str) -> str:
        """Generate a unique filename for downloads by appending (1), (2), etc., if a file already exists."""
        base, ext = os.path.splitext(filename)
        counter = 1
        new_filename = filename
        while os.path.exists(os.path.join(directory, new_filename)):
            new_filename = f"{base} ({counter}){ext}"
            counter += 1
        return new_filename

    @staticmethod
    def _convert_simple_xpath_to_css_selector(xpath: str) -> str:
        """Converts simple XPath expressions to CSS selectors."""
        if not xpath:
            return ""

        # Remove leading slash if present
        xpath = xpath.lstrip("/")

        # Split into parts
        parts = xpath.split("/")
        css_parts = []

        for part in parts:
            if not part:
                continue

            # Handle custom elements with colons by escaping them
            if ":" in part and "[" not in part:
                base_part = part.replace(":", r"\:")
                css_parts.append(base_part)
                continue

            # Handle index notation [n]
            if "[" in part:
                base_part = part[: part.find("[")]
                # Handle custom elements with colons in the base part
                if ":" in base_part:
                    base_part = base_part.replace(":", r"\:")
                index_part = part[part.find("[") :]

                # Handle multiple indices
                indices = [i.strip("[]") for i in index_part.split("]")[:-1]]

                for idx in indices:
                    try:
                        # Handle numeric indices
                        if idx.isdigit():
                            index = int(idx) - 1
                            base_part += f":nth-of-type({index + 1})"
                        # Handle last() function
                        elif idx == "last()":
                            base_part += ":last-of-type"
                        # Handle position() functions
                        elif "position()" in idx:
                            if ">1" in idx:
                                base_part += ":nth-of-type(n+2)"
                    except ValueError:
                        continue

                css_parts.append(base_part)
            else:
                css_parts.append(part)

        base_selector = " > ".join(css_parts)
        return base_selector

    @classmethod
    @time_execution_sync("--enhanced_css_selector_for_element")
    def _enhanced_css_selector_for_element(
        cls, element: DOMElementNode, include_dynamic_attributes: bool = True
    ) -> str:
        """
        Creates a CSS selector for a DOM element, handling various edge cases and special characters.

        Args:
                        element: The DOM element to create a selector for

        Returns:
                        A valid CSS selector string
        """
        try:
            # Get base selector from XPath
            css_selector = cls._convert_simple_xpath_to_css_selector(element.xpath)

            # Handle class attributes
            if (
                "class" in element.attributes
                and element.attributes["class"]
                and include_dynamic_attributes
            ):
                # Define a regex pattern for valid class names in CSS
                valid_class_name_pattern = re.compile(r"^[a-zA-Z_][a-zA-Z0-9_-]*$")

                # Iterate through the class attribute values
                classes = element.attributes["class"].split()
                for class_name in classes:
                    # Skip empty class names
                    if not class_name.strip():
                        continue

                    # Check if the class name is valid
                    if valid_class_name_pattern.match(class_name):
                        # Append the valid class name to the CSS selector
                        css_selector += f".{class_name}"
                    else:
                        # Skip invalid class names
                        continue

            # Expanded set of safe attributes that are stable and useful for selection
            SAFE_ATTRIBUTES = {
                # Data attributes (if they're stable in your application)
                "id",
                # Standard HTML attributes
                "name",
                "type",
                "placeholder",
                # Accessibility attributes
                "aria-label",
                "aria-labelledby",
                "aria-describedby",
                "role",
                # Common form attributes
                "for",
                "autocomplete",
                "required",
                "readonly",
                # Media attributes
                "alt",
                "title",
                "src",
                # Custom stable attributes (add any application-specific ones)
                "href",
                "target",
            }

            if include_dynamic_attributes:
                dynamic_attributes = {
                    "data-id",
                    "data-qa",
                    "data-cy",
                    "data-testid",
                }
                SAFE_ATTRIBUTES.update(dynamic_attributes)

            # Handle other attributes
            for attribute, value in element.attributes.items():
                if attribute == "class":
                    continue

                # Skip invalid attribute names
                if not attribute.strip():
                    continue

                if attribute not in SAFE_ATTRIBUTES:
                    continue

                # Escape special characters in attribute names
                safe_attribute = attribute.replace(":", r"\:")

                # Handle different value cases
                if value == "":
                    css_selector += f"[{safe_attribute}]"
                elif any(char in value for char in "\"'<>`\n\r\t"):
                    # Use contains for values with special characters
                    # For newline-containing text, only use the part before the newline
                    if "\n" in value:
                        value = value.split("\n")[0]
                    # Regex-substitute *any* whitespace with a single space, then strip.
                    collapsed_value = re.sub(r"\s+", " ", value).strip()
                    # Escape embedded double-quotes.
                    safe_value = collapsed_value.replace('"', '\\"')
                    css_selector += f'[{safe_attribute}*="{safe_value}"]'
                else:
                    css_selector += f'[{safe_attribute}="{value}"]'

            return css_selector

        except Exception:
            # Fallback to a more basic selector if something goes wrong
            tag_name = element.tag_name or "*"
            return f"{tag_name}[highlight_index='{element.highlight_index}']"

    @require_initialization
    @time_execution_async("--is_visible")
    async def _is_visible(self, element: ElementHandle) -> bool:
        """
        Checks if an element is visible on the page.
        We use our own implementation instead of relying solely on Playwright's is_visible() because
        of edge cases with CSS frameworks like Tailwind. When elements use Tailwind's 'hidden' class,
        the computed style may return display as '' (empty string) instead of 'none', causing Playwright
        to incorrectly consider hidden elements as visible. By additionally checking the bounding box
        dimensions, we catch elements that have zero width/height regardless of how they were hidden.
        """
        is_hidden = await element.is_hidden()
        bbox = await element.bounding_box()

        return (
            not is_hidden
            and bbox is not None
            and bbox["width"] > 0
            and bbox["height"] > 0
        )

    @require_initialization
    @time_execution_async("--get_locate_element")
    async def get_locate_element(self, element: DOMElementNode) -> ElementHandle | None:
        page = await self.get_current_page()
        current_frame = page

        # Start with the target element and collect all parents
        parents: list[DOMElementNode] = []
        current = element
        while current.parent is not None:
            parent = current.parent
            parents.append(parent)
            current = parent

        # Reverse the parents list to process from top to bottom
        parents.reverse()

        # Process all iframe parents in sequence
        iframes = [item for item in parents if item.tag_name == "iframe"]
        for parent in iframes:
            css_selector = self._enhanced_css_selector_for_element(
                parent,
                include_dynamic_attributes=self.browser_profile.include_dynamic_attributes,
            )
            current_frame = current_frame.frame_locator(css_selector)

        css_selector = self._enhanced_css_selector_for_element(
            element,
            include_dynamic_attributes=self.browser_profile.include_dynamic_attributes,
        )

        try:
            if isinstance(current_frame, FrameLocator):
                element_handle = await current_frame.locator(
                    css_selector
                ).element_handle()
                return element_handle
            else:
                # Try to scroll into view if hidden
                element_handle = await current_frame.query_selector(css_selector)
                if element_handle:
                    is_visible = await self._is_visible(element_handle)
                    if is_visible:
                        await element_handle.scroll_into_view_if_needed()
                    return element_handle
                return None
        except Exception as e:
            logger.error(f"❌  Failed to locate element: {str(e)}")
            return None

    @require_initialization
    @time_execution_async("--get_locate_element_by_xpath")
    async def get_locate_element_by_xpath(self, xpath: str) -> ElementHandle | None:
        """
        Locates an element on the page using the provided XPath.
        """
        page = await self.get_current_page()

        try:
            # Use XPath to locate the element
            element_handle = await page.query_selector(f"xpath={xpath}")
            if element_handle:
                is_visible = await self._is_visible(element_handle)
                if is_visible:
                    await element_handle.scroll_into_view_if_needed()
                return element_handle
            return None
        except Exception as e:
            logger.error(f"❌  Failed to locate element by XPath {xpath}: {str(e)}")
            return None

    @require_initialization
    @time_execution_async("--get_locate_element_by_css_selector")
    async def get_locate_element_by_css_selector(
        self, css_selector: str
    ) -> ElementHandle | None:
        """
        Locates an element on the page using the provided CSS selector.
        """
        page = await self.get_current_page()

        try:
            # Use CSS selector to locate the element
            element_handle = await page.query_selector(css_selector)
            if element_handle:
                is_visible = await self._is_visible(element_handle)
                if is_visible:
                    await element_handle.scroll_into_view_if_needed()
                return element_handle
            return None
        except Exception as e:
            logger.error(
                f"❌  Failed to locate element by CSS selector {css_selector}: {str(e)}"
            )
            return None

    @require_initialization
    @time_execution_async("--get_locate_element_by_text")
    async def get_locate_element_by_text(
        self, text: str, nth: int | None = 0, element_type: str | None = None
    ) -> ElementHandle | None:
        """
        Locates an element on the page using the provided text.
        If `nth` is provided, it returns the nth matching element (0-based).
        If `element_type` is provided, filters by tag name (e.g., 'button', 'span').
        """
        page = await self.get_current_page()
        try:
            # handle also specific element type or use any type.
            selector = f'{element_type or "*"}:text("{text}")'
            elements = await page.query_selector_all(selector)
            # considering only visible elements
            elements = [el for el in elements if await self._is_visible(el)]

            if not elements:
                logger.error(f"No visible element with text '{text}' found.")
                return None

            if nth is not None:
                if 0 <= nth < len(elements):
                    element_handle = elements[nth]
                else:
                    logger.error(
                        f"Visible element with text '{text}' not found at index {nth}."
                    )
                    return None
            else:
                element_handle = elements[0]

            is_visible = await self._is_visible(element_handle)
            if is_visible:
                await element_handle.scroll_into_view_if_needed()
            return element_handle
        except Exception as e:
            logger.error(f"❌  Failed to locate element by text '{text}': {str(e)}")
            return None

    @require_initialization
    @time_execution_async("--input_text_element_node")
    async def _input_text_element_node(self, element_node: DOMElementNode, text: str):
        """
        Input text into an element with proper error handling and state management.
        Handles different types of input fields and ensures proper element state before input.
        """
        try:
            # Highlight before typing
            # if element_node.highlight_index is not None:
            # 	await self._update_state(focus_element=element_node.highlight_index)

            element_handle = await self.get_locate_element(element_node)

            if element_handle is None:
                raise BrowserError(f"Element: {repr(element_node)} not found")

            # Ensure element is ready for input
            try:
                await element_handle.wait_for_element_state("stable", timeout=1000)
                is_visible = await self._is_visible(element_handle)
                if is_visible:
                    await element_handle.scroll_into_view_if_needed(timeout=1000)
            except Exception:
                pass

            # Get element properties to determine input method
            tag_handle = await element_handle.get_property("tagName")
            tag_name = (await tag_handle.json_value()).lower()
            is_contenteditable = await element_handle.get_property("isContentEditable")
            readonly_handle = await element_handle.get_property("readOnly")
            disabled_handle = await element_handle.get_property("disabled")

            readonly = await readonly_handle.json_value() if readonly_handle else False
            disabled = await disabled_handle.json_value() if disabled_handle else False

            # always click the element first to make sure it's in the focus
            await element_handle.click()
            await asyncio.sleep(0.1)

            try:
                if (
                    await is_contenteditable.json_value() or tag_name == "input"
                ) and not (readonly or disabled):
                    await element_handle.evaluate(
                        'el => {el.textContent = ""; el.value = "";}'
                    )
                    await element_handle.type(text, delay=5)
                else:
                    await element_handle.fill(text)
            except Exception:
                # last resort fallback, assume it's already focused after we clicked on it,
                # just simulate keypresses on the entire page
                page = await self.get_current_page()
                await page.keyboard.type(text)

        except Exception as e:
            logger.debug(
                f"❌  Failed to input text into element: {repr(element_node)}. Error: {str(e)}"
            )
            raise BrowserError(
                f"Failed to input text into index {element_node.highlight_index}"
            )

    @require_initialization
    @time_execution_async("--switch_to_tab")
    async def switch_to_tab(self, page_id: int) -> Page:
        """Switch to a specific tab by its page_id (aka tab index exposed to LLM)"""
        assert self.browser_context is not None, "Browser context is not set"
        pages = self.browser_context.pages

        if page_id >= len(pages):
            raise BrowserError(f"No tab found with page_id: {page_id}")

        page = pages[page_id]

        # Check if the tab's URL is allowed before switching
        if not self._is_url_allowed(page.url):
            raise BrowserError(f"Cannot switch to tab with non-allowed URL: {page.url}")

        # Update both tab references - agent wants this tab, and it's now in the foreground
        self.agent_current_page = page
        self.human_current_page = page

        # Bring tab to front and wait for it to load
        await page.bring_to_front()
        await page.wait_for_load_state()

        # Set the viewport size for the tab
        if self.browser_profile.viewport:
            await page.set_viewport_size(self.browser_profile.viewport)

        return page

    @time_execution_async("--create_new_tab")
    async def create_new_tab(self, url: str | None = None) -> Page:
        """Create a new tab and optionally navigate to a URL"""

        if url and not self._is_url_allowed(url):
            raise BrowserError(f"Cannot create new tab with non-allowed URL: {url}")

        new_page = await self.browser_context.new_page()

        # Update agent tab reference
        self.agent_current_page = new_page

        # Update human tab reference if there is no human tab yet
        if (not self.human_current_page) or self.human_current_page.is_closed():
            self.human_current_page = new_page

        await new_page.wait_for_load_state()

        # Set the viewport size for the new tab
        if self.browser_profile.viewport:
            await new_page.set_viewport_size(self.browser_profile.viewport)

        if url:
            await new_page.goto(url, wait_until="domcontentloaded", timeout=10000)
            await self._wait_for_page_and_frames_load(timeout_overwrite=1)

        assert self.human_current_page is not None
        assert self.agent_current_page is not None
        # if url:  # sometimes this does not pass because JS or HTTP redirects the page really fast
        # 	assert self.agent_current_page.url == url
        # else:
        # 	assert self.agent_current_page.url == 'about:blank'

        # if there are any unused about:blank tabs after we open a new tab, close them to clean up unused tabs
        for page in self.browser_context.pages:
            if page.url == "about:blank" and page != self.agent_current_page:
                await page.close()
                self.human_current_page = (  # in case we just closed the human's tab, fix the refs
                    self.human_current_page
                    if not self.human_current_page.is_closed()
                    else self.agent_current_page
                )

        return new_page

    # region - Helper methods for easier access to the DOM

    @require_initialization
    async def get_selector_map(self) -> SelectorMap:
        if self._cached_browser_state_summary is None:
            return {}
        return self._cached_browser_state_summary.selector_map

    @require_initialization
    async def get_element_by_index(self, index: int) -> ElementHandle | None:
        selector_map = await self.get_selector_map()
        element_handle = await self.get_locate_element(selector_map[index])
        return element_handle

    @require_initialization
    async def find_file_upload_element_by_index(
        self, index: int
    ) -> DOMElementNode | None:
        """
        Find a file upload element related to the element at the given index:
        - Check if the element itself is a file input
        - Check if it's a label pointing to a file input
        - Recursively search children for file inputs
        - Check siblings for file inputs

        Args:
                index: The index of the candidate element (could be a file input, label, or parent element)

        Returns:
                The DOM element for the file input if found, None otherwise
        """
        try:
            selector_map = await self.get_selector_map()
            if index not in selector_map:
                return None

            candidate_element = selector_map[index]

            def is_file_input(node: DOMElementNode) -> bool:
                return (
                    isinstance(node, DOMElementNode)
                    and node.tag_name == "input"
                    and node.attributes.get("type") == "file"
                )

            def find_element_by_id(
                node: DOMElementNode, element_id: str
            ) -> DOMElementNode | None:
                if isinstance(node, DOMElementNode):
                    if node.attributes.get("id") == element_id:
                        return node
                    for child in node.children:
                        result = find_element_by_id(child, element_id)
                        if result:
                            return result
                return None

            def get_root(node: DOMElementNode) -> DOMElementNode:
                root = node
                while root.parent:
                    root = root.parent
                return root

            # Recursively search for file input in node and its children
            def find_file_input_recursive(
                node: DOMElementNode, max_depth: int = 3, current_depth: int = 0
            ) -> DOMElementNode | None:
                if current_depth > max_depth or not isinstance(node, DOMElementNode):
                    return None

                # Check current element
                if is_file_input(node):
                    return node

                # Recursively check children
                if node.children and current_depth < max_depth:
                    for child in node.children:
                        if isinstance(child, DOMElementNode):
                            result = find_file_input_recursive(
                                child, max_depth, current_depth + 1
                            )
                            if result:
                                return result
                return None

            # Check if current element is a file input
            if is_file_input(candidate_element):
                return candidate_element

            # Check if it's a label pointing to a file input
            if (
                candidate_element.tag_name == "label"
                and candidate_element.attributes.get("for")
            ):
                input_id = candidate_element.attributes.get("for")
                root_element = get_root(candidate_element)

                target_input = find_element_by_id(root_element, input_id)
                if target_input and is_file_input(target_input):
                    return target_input

            # Recursively check children
            child_result = find_file_input_recursive(candidate_element)
            if child_result:
                return child_result

            # Check siblings
            if candidate_element.parent:
                for sibling in candidate_element.parent.children:
                    if sibling is not candidate_element and isinstance(
                        sibling, DOMElementNode
                    ):
                        if is_file_input(sibling):
                            return sibling
            return None

        except Exception as e:
            logger.debug(f"Error in find_file_upload_element_by_index: {e}")
            return None

    @require_initialization
    async def get_scroll_info(self, page: Page) -> tuple[int, int]:
        """Get scroll position information for the current page."""
        scroll_y = await page.evaluate("window.scrollY")
        viewport_height = await page.evaluate("window.innerHeight")
        total_height = await page.evaluate("document.documentElement.scrollHeight")
        pixels_above = scroll_y
        pixels_below = total_height - (scroll_y + viewport_height)
        return pixels_above, pixels_below

    @require_initialization
    async def _scroll_container(self, pixels: int) -> None:
        """Scroll the element that truly owns vertical scroll.Starts at the focused node ➜ climbs to the first big, scroll-enabled ancestor otherwise picks the first scrollable element or the root, then calls `element.scrollBy` (or `window.scrollBy` for the root) by the supplied pixel value."""

        # An element can *really* scroll if: overflow-y is auto|scroll|overlay, it has more content than fits, its own viewport is not a postage stamp (more than 50 % of window).
        SMART_SCROLL_JS = """(dy) => {
			const bigEnough = el => el.clientHeight >= window.innerHeight * 0.5;
			const canScroll = el =>
				el &&
				/(auto|scroll|overlay)/.test(getComputedStyle(el).overflowY) &&
				el.scrollHeight > el.clientHeight &&
				bigEnough(el);

			let el = document.activeElement;
			while (el && !canScroll(el) && el !== document.body) el = el.parentElement;

			el = canScroll(el)
					? el
					: [...document.querySelectorAll('*')].find(canScroll)
					|| document.scrollingElement
					|| document.documentElement;

			if (el === document.scrollingElement ||
				el === document.documentElement ||
				el === document.body) {
				window.scrollBy(0, dy);
			} else {
				el.scrollBy({ top: dy, behavior: 'auto' });
			}
		}"""
        page = await self.get_current_page()
        await page.evaluate(SMART_SCROLL_JS, pixels)

    # --- DVD Screensaver Loading Animation Helper ---
    async def _show_dvd_screensaver_loading_animation(self, page: Page) -> None:
        """
        Injects a DVD screensaver-style bouncing logo loading animation overlay into the given Playwright Page.
        This is used to visually indicate that the browser is setting up or waiting.
        """
        await page.evaluate("""() => {
			document.title = 'Setting up...';

			// Create the main overlay
			const loadingOverlay = document.createElement('div');
			loadingOverlay.id = 'pretty-loading-animation';
			loadingOverlay.style.position = 'fixed';
			loadingOverlay.style.top = '0';
			loadingOverlay.style.left = '0';
			loadingOverlay.style.width = '100vw';
			loadingOverlay.style.height = '100vh';
			loadingOverlay.style.background = '#000';
			loadingOverlay.style.zIndex = '99999';
			loadingOverlay.style.overflow = 'hidden';

			// Create the image element
			const img = document.createElement('img');
			img.src = 'data:image/png;base64,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'
			img.alt = 'Anotherme';
			img.style.width = '600px';
			img.style.height = 'auto';
			img.style.position = 'absolute';
			img.style.left = '0px';
			img.style.top = '0px';
			img.style.zIndex = '2';
			img.style.opacity = '0.8';

			loadingOverlay.appendChild(img);
			document.body.appendChild(loadingOverlay);

			// DVD screensaver bounce logic
			let x = Math.random() * (window.innerWidth - 300);
			let y = Math.random() * (window.innerHeight - 300);
			let dx = 1.2 + Math.random() * 0.4; // px per frame
			let dy = 1.2 + Math.random() * 0.4;
			// Randomize direction
			if (Math.random() > 0.5) dx = -dx;
			if (Math.random() > 0.5) dy = -dy;

			function animate() {
				const imgWidth = img.offsetWidth || 300;
				const imgHeight = img.offsetHeight || 300;
				x += dx;
				y += dy;

				if (x <= 0) {
					x = 0;
					dx = Math.abs(dx);
				} else if (x + imgWidth >= window.innerWidth) {
					x = window.innerWidth - imgWidth;
					dx = -Math.abs(dx);
				}
				if (y <= 0) {
					y = 0;
					dy = Math.abs(dy);
				} else if (y + imgHeight >= window.innerHeight) {
					y = window.innerHeight - imgHeight;
					dy = -Math.abs(dy);
				}

				img.style.left = `${x}px`;
				img.style.top = `${y}px`;

				requestAnimationFrame(animate);
			}
			animate();

			// Responsive: update bounds on resize
			window.addEventListener('resize', () => {
				x = Math.min(x, window.innerWidth - img.offsetWidth);
				y = Math.min(y, window.innerHeight - img.offsetHeight);
			});

			// Add a little CSS for smoothness
			const style = document.createElement('style');
			style.innerHTML = `
				#pretty-loading-animation {
					/*backdrop-filter: blur(2px) brightness(0.9);*/
				}
				#pretty-loading-animation img {
					user-select: none;
					pointer-events: none;
				}
			`;
			document.head.appendChild(style);
		}""")
