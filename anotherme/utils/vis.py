import tkinter as tk
import shutil
import os
import pyfiglet
from termcolor import colored
from typing import Dict


def logo():
    os.system("cls" if os.name == "nt" else "clear")
    terminal_width = shutil.get_terminal_size().columns

    simplex_art = pyfiglet.figlet_format("Simplex ", font="slant")
    ai_art = pyfiglet.figlet_format("AI", font="graffiti")

    simplex_lines = simplex_art.split("\n")
    ai_lines = ai_art.split("\n")

    for i in range(max(len(simplex_lines), len(ai_lines))):
        simplex_part = simplex_lines[i] if i < len(simplex_lines) else ""
        ai_part = ai_lines[i] if i < len(ai_lines) else ""

        simplex_bold = colored(simplex_part, attrs=["bold"])
        ai_bold_blue = colored(ai_part, color="blue", attrs=["bold"])
        combined = simplex_bold + ai_bold_blue

        print(combined.center(terminal_width + 20))


def get_screen_dimensions():
    root = tk.Tk()
    root.withdraw()
    screen_w = root.winfo_screenwidth()
    screen_h = root.winfo_screenheight()
    root.destroy()
    return screen_w, screen_h


def get_terminal_pixel_size(char_width=7.5, char_height=20):
    size = shutil.get_terminal_size()
    print(size.columns * char_width, size.lines * char_height)
    return int(size.columns * char_width), int(size.lines * char_height)


def get_adjusted_window_size(
    char_width: float = 7.5,
    min_width: int = 800,
    min_height: int = 600,
    margin_ratio: float = 0.1,
    use_terminal_aware: bool = True,
) -> Dict[str, int]:
    """
    Get dynamically adjusted browser window size for browser_use1.

    Args:
        char_width: Average character width in pixels for terminal size calculation
        min_width: Minimum window width
        min_height: Minimum window height
        margin_ratio: Ratio of screen to leave as margin (0.1 = 10%)
        use_terminal_aware: Whether to account for terminal space

    Returns:
        Dict with 'width' and 'height' keys compatible with browser_use1
    """
    try:
        screen_w, screen_h = get_screen_dimensions()

        if use_terminal_aware:
            # Account for terminal space
            term_w, term_h = get_terminal_pixel_size(char_width=char_width)
            # Leave some margin and account for terminal
            available_width = screen_w - term_w
            available_height = screen_h
        else:
            # Use most of the screen with margin
            available_width = int(screen_w)
            available_height = int(screen_h)

        # Ensure minimum sizes
        width = max(available_width, min_width)
        height = max(available_height, min_height)

        return {"width": width, "height": height}

    except Exception as e:
        # Fallback to safe defaults if screen detection fails
        print(f"Warning: Could not detect screen dimensions ({e}), using defaults")
        return {"width": min_width, "height": min_height}


def get_responsive_window_size(screen_size: str = "auto") -> Dict[str, int]:
    """
    Get responsive window size based on common screen sizes.

    Args:
        screen_size: "auto", "small", "medium", "large", "fullhd", "4k"

    Returns:
        Dict with 'width' and 'height' keys
    """
    if screen_size == "auto":
        return get_adjusted_window_size()

    size_presets = {
        "small": {"width": 1024, "height": 768},
        "medium": {"width": 1366, "height": 768},
        "large": {"width": 1920, "height": 1080},
        "fullhd": {"width": 1920, "height": 1080},
        "4k": {"width": 3840, "height": 2160},
    }

    return size_presets.get(screen_size, {"width": 1920, "height": 1080})
