from playwright.async_api import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Page
from anotherme.browser_use.browser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserSession
from anotherme.browser_use.agent.service import Agent
from anotherme.browser_use.browser.session import DEFAULT_BROWSER_PROFILE


class BrowserWithOutAgent(Agent):
    def __init__(
        self,
        page: Page | None = None,
        browser: <PERSON><PERSON><PERSON> | None = None,
        browser_context: BrowserContext | None = None,
        browser_profile: BrowserProfile | None = None,
        browser_session: BrowserSession | None = None,
    ):
        browser_context = page.context if page else browser_context
        browser_profile = browser_profile or DEFAULT_BROWSER_PROFILE
        self.browser_session = browser_session or BrowserSession(
            profile=browser_profile,
            browser=browser,
            browser_context=browser_context,
            page=page,
        )
