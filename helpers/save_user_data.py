#!/usr/bin/env python3
"""
Save User Data

Opens a browser for user interaction, automatically saves login data and user information
when the user finishes and presses Enter.

Usage:
    python helpers/save_user_data.py
    python helpers/save_user_data.py --url https://www.google.com
"""

import asyncio
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from anotherme.browser_use.browser.session import BrowserSession  # noqa: E402
from anotherme.browser_use.browser.profile import (  # noqa: E402
    BrowserProfile,
    BROWSERUSE_PROFILES_DIR,
)  # noqa: E402


async def main():
    """Main function: Start browser and wait for user interaction"""

    # Parse command line arguments
    import argparse

    parser = argparse.ArgumentParser(description="Simple interactive browser")
    parser.add_argument("--url", type=str, help="URL to open on startup")
    parser.add_argument("--user-data-dir", type=str, help="Custom user data directory")
    args = parser.parse_args()

    # Set user data directory
    if args.user_data_dir:
        user_data_dir = Path(args.user_data_dir).expanduser().resolve()
    else:
        user_data_dir = BROWSERUSE_PROFILES_DIR / "default"

    print("🌟 Starting Interactive Browser")
    print("=" * 40)
    print(f"📁 User data directory: {user_data_dir}")

    # Create browser profile
    browser_profile = BrowserProfile(
        user_data_dir=user_data_dir,
        headless=False,  # Window mode
        window_size={"width": 1280, "height": 800},
    )

    # Prepare user data directory
    # user_data_dir.mkdir(parents=True, exist_ok=True)
    browser_profile.prepare_user_data_dir()

    browser_session = None

    try:
        print("🚀 Starting browser...")

        # Create and start browser session
        browser_session = BrowserSession(browser_profile=browser_profile)
        await browser_session.start()

        # Get or create page
        if not browser_session.browser_context.pages:
            page = await browser_session.browser_context.new_page()
        else:
            page = browser_session.browser_context.pages[0]

        print("✅ Browser started successfully!")

        # Navigate to URL if specified
        if args.url:
            print(f"🔗 Opening: {args.url}")
            await page.goto(args.url)

        # Wait for user interaction
        print("\n" + "=" * 50)
        print("🎯 Browser is ready for interaction!")
        print(
            "💾 All your actions (login info, cookies, etc.) will be saved automatically"
        )
        print("⏎  Press Enter when finished")
        print("=" * 50)

        # Wait for user to press Enter
        input()

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()

    finally:
        # Close browser
        if browser_session:
            print("\n🛑 Closing browser...")
            await browser_session.close()
            print("✅ Browser closed")
            print(f"💾 User data saved to: {user_data_dir}")


if __name__ == "__main__":
    asyncio.run(main())
