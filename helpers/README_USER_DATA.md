# Save User Data

This tool opens a browser for user interaction, automatically saves login information and user data when the user finishes and presses Enter.

## Features

- 🔄 **Automatic User Data Saving**: Automatically saves cookies, localStorage, sessionStorage and other browser data
- 📁 **Default Configuration Directory**: Uses `~/.config/browseruse/profiles/default` as the default user data directory
- 🎯 **Simple to Use**: Open browser → User interaction → Press Enter to finish
- 💾 **Persistent Storage**: Automatically loads previously saved user data on next startup

## Usage

### Basic Usage

```bash
# Start browser (blank page)
python helpers/save_user_data.py

# Start with a specific website
python helpers/save_user_data.py --url https://www.google.com

# Use custom user data directory
python helpers/save_user_data.py --user-data-dir ~/my_browser_profile
```

### Workflow

1. Run the script to start the browser
2. Perform any operations in the browser (login to websites, browse pages, etc.)
3. When finished, press Enter in the terminal
4. Browser closes and all data is automatically saved

### Example Session

```
🌟 Starting Interactive Browser
========================================
📁 User data directory: ~/.config/browseruse/profiles/default
🚀 Starting browser...
✅ Browser started successfully!

==================================================
🎯 Browser is ready for interaction!
💾 All your actions (login info, cookies, etc.) will be saved automatically
⏎  Press Enter when finished
==================================================

[User performs operations in browser...]
[Press Enter]

🛑 Closing browser...
✅ Browser closed
💾 User data saved to: ~/.config/browseruse/profiles/default
```

## Data Persistence

### Automatically Saved Data Types

- **Cookies**: Login status, preferences, etc.
- **Local Storage**: Website local storage data
- **Session Storage**: Session storage data
- **IndexedDB**: Browser database
- **Cache**: Browser cache
- **History**: Browsing history

### Data Storage Location

Default storage location:
```
~/.config/browseruse/profiles/default/
```

You can customize the directory using the `--user-data-dir` parameter.

## Important Notes

1. **Single Instance**: Only one browser process can use the same user data directory at a time
2. **Automatic Cleanup**: The tool automatically cleans up browser lock files to prevent startup failures
3. **Data Security**: User data is stored locally, please keep it secure

## Troubleshooting

### Common Issues

**Cannot start browser**
- Ensure Playwright browsers are installed: `playwright install chromium`
- Check system permissions

**Data not saved**
- Verify the user data directory path is correct
- Check directory read/write permissions

### Dependencies

Make sure the necessary dependencies are installed:
```bash
pip install playwright
playwright install chromium
```
